// ai.go - AI 助手模組，整合 Claude AI、Gemini AI 等
package main

import (
	"context"
	"fmt"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

// AIConfig AI 助手配置
type AIConfig struct {
	// Claude AI 配置
	ClaudeAPIKey  string
	ClaudeModel   string // claude-3-sonnet-20240229, claude-3-haiku-20240307
	ClaudeBaseURL string

	// Gemini AI 配置
	GeminiAPIKey  string
	GeminiModel   string // gemini-pro, gemini-pro-vision
	GeminiBaseURL string

	// OpenAI 配置 (備用)
	OpenAIAPIKey  string
	OpenAIModel   string
	OpenAIBaseURL string

	// LangChain 配置
	LangChainEnabled bool
	LangChainConfig  map[string]interface{}

	// 一般設定
	DefaultProvider string // "claude", "gemini", "openai"
	MaxTokens       int
	Temperature     float32
	SystemPrompt    string
}

// AIProvider AI 提供者介面
type AIProvider interface {
	Name() string
	SendMessage(ctx context.Context, message string) (*AIResponse, error)
	SendMessageWithContext(ctx context.Context, message string, context []string) (*AIResponse, error)
	GetModels() []string
	IsAvailable() bool
}

// AIResponse AI 回應
type AIResponse struct {
	Content      string
	Provider     string
	Model        string
	TokensUsed   int
	ResponseTime time.Duration
	Error        error
}

// ConversationMessage 對話訊息
type ConversationMessage struct {
	Role      string // "user", "assistant", "system"
	Content   string
	Timestamp time.Time
	Provider  string
	Metadata  map[string]interface{}
}

// AIAssistant AI 助手主要結構
type AIAssistant struct {
	config       *AIConfig
	providers    map[string]AIProvider
	conversation []ConversationMessage

	// UI 元件
	content        fyne.CanvasObject
	chatHistory    *widget.List
	messageInput   *widget.Entry
	sendButton     *widget.Button
	providerSelect *widget.Select
	modelSelect    *widget.Select
	settingsPanel  *fyne.Container
	contextPanel   *fyne.Container

	// 資料綁定
	currentMessage binding.String
	isProcessing   binding.Bool

	// 開發助手功能
	codeAnalyzer   *CodeAnalyzer
	projectHelper  *ProjectHelper
	debugAssistant *DebugAssistant
}

// NewAIAssistant 建立新的 AI 助手
func NewAIAssistant(config *AIConfig) *AIAssistant {
	a := &AIAssistant{
		config:         config,
		providers:      make(map[string]AIProvider),
		conversation:   []ConversationMessage{},
		currentMessage: binding.NewString(),
		isProcessing:   binding.NewBool(),
	}

	// 初始化 AI 提供者
	a.initializeProviders()

	// 初始化開發助手功能
	a.codeAnalyzer = NewCodeAnalyzer()
	a.projectHelper = NewProjectHelper()
	a.debugAssistant = NewDebugAssistant()

	// 建立 UI
	a.buildUI()

	// 添加歡迎訊息
	a.addWelcomeMessage()

	return a
}

// initializeProviders 初始化 AI 提供者
func (a *AIAssistant) initializeProviders() {
	// Claude AI
	if a.config.ClaudeAPIKey != "" {
		a.providers["claude"] = NewClaudeProvider(a.config)
	}

	// Gemini AI
	if a.config.GeminiAPIKey != "" {
		a.providers["gemini"] = NewGeminiProvider(a.config)
	}

	// OpenAI (備用)
	if a.config.OpenAIAPIKey != "" {
		a.providers["openai"] = NewOpenAIProvider(a.config)
	}
}

// buildUI 建立使用者介面
func (a *AIAssistant) buildUI() {
	// 建立聊天歷史
	a.createChatHistory()

	// 建立輸入區域
	inputArea := a.createInputArea()

	// 建立設定面板
	a.createSettingsPanel()

	// 建立上下文面板
	a.createContextPanel()

	// 建立工具列
	toolbar := a.createToolbar()

	// 主要聊天區域
	chatArea := container.NewBorder(
		toolbar,
		inputArea,
		nil,
		nil,
		a.chatHistory,
	)

	// 側邊面板
	sidePanel := container.NewAppTabs(
		container.NewTabItem("設定", a.settingsPanel),
		container.NewTabItem("上下文", a.contextPanel),
		container.NewTabItem("開發助手", a.createDevAssistantPanel()),
	)

	// 組合主要佈局
	a.content = container.NewHSplit(
		chatArea,
		sidePanel,
	)
}

// createChatHistory 建立聊天歷史
func (a *AIAssistant) createChatHistory() {
	a.chatHistory = widget.NewList(
		func() int {
			return len(a.conversation)
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				container.NewHBox(
					widget.NewIcon(theme.AccountIcon()),
					widget.NewLabelWithStyle("角色", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
					widget.NewLabel("時間"),
				),
				widget.NewRichTextFromMarkdown("訊息內容"),
			)
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			if id >= len(a.conversation) {
				return
			}

			msg := a.conversation[id]
			container := item.(*fyne.Container)

			// 更新標題列
			header := container.Objects[0].(*fyne.Container)
			icon := header.Objects[0].(*widget.Icon)
			roleLabel := header.Objects[1].(*widget.Label)
			timeLabel := header.Objects[2].(*widget.Label)

			// 根據角色設定圖示和顏色
			switch msg.Role {
			case "user":
				icon.SetResource(theme.AccountIcon())
				roleLabel.SetText("您")
			case "assistant":
				icon.SetResource(theme.ComputerIcon())
				roleLabel.SetText("AI 助手")
			case "system":
				icon.SetResource(theme.InfoIcon())
				roleLabel.SetText("系統")
			}

			timeLabel.SetText(msg.Timestamp.Format("15:04"))

			// 更新訊息內容
			content := container.Objects[1].(*widget.RichText)
			content.ParseMarkdown(msg.Content)
		},
	)
}

// createInputArea 建立輸入區域
func (a *AIAssistant) createInputArea() *fyne.Container {
	// 訊息輸入框
	a.messageInput = widget.NewMultiLineEntry()
	a.messageInput.Bind(a.currentMessage)
	a.messageInput.SetPlaceHolder("輸入您的問題或請求...")
	a.messageInput.Wrapping = fyne.TextWrapWord

	// 發送按鈕
	a.sendButton = widget.NewButtonWithIcon("發送", theme.MailSendIcon(), a.sendMessage)

	// 快速動作按鈕
	quickActions := container.NewHBox(
		widget.NewButtonWithIcon("程式碼分析", theme.DocumentIcon(), a.analyzeCode),
		widget.NewButtonWithIcon("專案協助", theme.FolderIcon(), a.projectAssist),
		widget.NewButtonWithIcon("除錯協助", theme.BrokenImageIcon(), a.debugAssist),
		widget.NewButtonWithIcon("清空對話", theme.DeleteIcon(), a.clearConversation),
	)

	// 組合輸入區域
	return container.NewBorder(
		quickActions,
		container.NewHBox(
			widget.NewLabel(""),
			a.sendButton,
		),
		nil,
		nil,
		container.NewScroll(a.messageInput),
	)
}

// createSettingsPanel 建立設定面板
func (a *AIAssistant) createSettingsPanel() {
	// 提供者選擇
	providers := []string{}
	for name := range a.providers {
		providers = append(providers, name)
	}
	a.providerSelect = widget.NewSelect(providers, a.onProviderChanged)
	a.providerSelect.SetSelected(a.config.DefaultProvider)

	// 模型選擇
	a.modelSelect = widget.NewSelect([]string{}, a.onModelChanged)
	a.updateModelList()

	// 設定項目
	a.settingsPanel = container.NewVBox(
		widget.NewLabelWithStyle("AI 設定", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		widget.NewSeparator(),

		widget.NewLabel("AI 提供者:"),
		a.providerSelect,

		widget.NewLabel("模型:"),
		a.modelSelect,

		widget.NewLabel("系統提示:"),
		widget.NewMultiLineEntry(), // TODO: 綁定到系統提示

		widget.NewSeparator(),
		widget.NewButton("儲存設定", a.saveSettings),
		widget.NewButton("重設設定", a.resetSettings),
	)
}

// createContextPanel 建立上下文面板
func (a *AIAssistant) createContextPanel() {
	a.contextPanel = container.NewVBox(
		widget.NewLabelWithStyle("對話上下文", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		widget.NewSeparator(),
		widget.NewLabel("專案資訊: 載入中..."),
		widget.NewLabel("當前檔案: 無"),
		widget.NewLabel("Git 狀態: 未知"),
		widget.NewSeparator(),
		widget.NewButton("重新載入上下文", a.refreshContext),
	)
}

// createDevAssistantPanel 建立開發助手面板
func (a *AIAssistant) createDevAssistantPanel() *fyne.Container {
	return container.NewVBox(
		widget.NewLabelWithStyle("開發助手工具", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		widget.NewSeparator(),

		widget.NewButton("程式碼審查", func() {
			a.addSystemMessage("開始程式碼審查...")
		}),
		widget.NewButton("生成測試", func() {
			a.addSystemMessage("生成單元測試...")
		}),
		widget.NewButton("重構建議", func() {
			a.addSystemMessage("分析重構機會...")
		}),
		widget.NewButton("效能優化", func() {
			a.addSystemMessage("分析效能瓶頸...")
		}),

		widget.NewSeparator(),
		widget.NewLabel("AI 整合狀態:"),
		widget.NewLabel("✓ Claude AI 已連接"),
		widget.NewLabel("✓ Gemini AI 已連接"),
	)
}

// createToolbar 建立工具列
func (a *AIAssistant) createToolbar() *widget.Toolbar {
	return widget.NewToolbar(
		widget.NewToolbarAction(theme.DocumentCreateIcon(), func() {
			a.addSystemMessage("開始新對話...")
		}),
		widget.NewToolbarSeparator(),
		widget.NewToolbarAction(theme.DocumentSaveIcon(), func() {
			a.saveConversation()
		}),
		widget.NewToolbarAction(theme.FolderOpenIcon(), func() {
			a.loadConversation()
		}),
		widget.NewToolbarSeparator(),
		widget.NewToolbarAction(theme.SettingsIcon(), func() {
			a.showAdvancedSettings()
		}),
	)
}

// sendMessage 發送訊息
func (a *AIAssistant) sendMessage() {
	message, _ := a.currentMessage.Get()
	if strings.TrimSpace(message) == "" {
		return
	}

	// 添加用戶訊息到對話
	a.addUserMessage(message)

	// 清空輸入框
	a.currentMessage.Set("")

	// 設定處理狀態
	a.isProcessing.Set(true)
	a.sendButton.Disable()

	// 在背景處理 AI 回應
	go a.processAIResponse(message)
}

// addUserMessage 添加用戶訊息
func (a *AIAssistant) addUserMessage(content string) {
	msg := ConversationMessage{
		Role:      "user",
		Content:   content,
		Timestamp: time.Now(),
		Provider:  a.config.DefaultProvider,
	}
	a.conversation = append(a.conversation, msg)
	a.chatHistory.Refresh()
	a.chatHistory.ScrollToBottom()
}

// addAssistantMessage 添加助手訊息
func (a *AIAssistant) addAssistantMessage(content, provider string) {
	msg := ConversationMessage{
		Role:      "assistant",
		Content:   content,
		Timestamp: time.Now(),
		Provider:  provider,
	}
	a.conversation = append(a.conversation, msg)
	a.chatHistory.Refresh()
	a.chatHistory.ScrollToBottom()
}

// addSystemMessage 添加系統訊息
func (a *AIAssistant) addSystemMessage(content string) {
	msg := ConversationMessage{
		Role:      "system",
		Content:   content,
		Timestamp: time.Now(),
		Provider:  "system",
	}
	a.conversation = append(a.conversation, msg)
	a.chatHistory.Refresh()
	a.chatHistory.ScrollToBottom()
}

// addWelcomeMessage 添加歡迎訊息
func (a *AIAssistant) addWelcomeMessage() {
	welcome := `# 歡迎使用 Koopa 的 AI 開發助手！

我是您的個人 AI 助手，專門為開發工作而設計。我可以幫助您：

## 🔧 開發協助
- **程式碼分析與審查**
- **生成單元測試**
- **重構建議**
- **效能優化建議**

## 🚀 專案管理
- **專案架構建議**
- **技術選型協助**
- **最佳實踐指導**

## 🐛 除錯協助
- **錯誤分析**
- **日誌解讀**
- **問題排查**

## 💡 其他功能
- **技術文件撰寫**
- **API 設計建議**
- **資料庫優化**

請隨時向我提問，或使用快速動作按鈕開始！`

	a.addAssistantMessage(welcome, "system")
}

// 實現缺失的方法

// processAIResponse 處理 AI 回應
func (a *AIAssistant) processAIResponse(message string) {
	defer func() {
		a.isProcessing.Set(false)
		a.sendButton.Enable()
	}()

	provider := a.providers[a.config.DefaultProvider]
	if provider == nil {
		a.addAssistantMessage("錯誤: 未找到可用的 AI 提供者", "error")
		return
	}

	// 模擬 AI 回應（實際應該調用真實的 AI API）
	response := fmt.Sprintf("這是來自 %s 的模擬回應：\n\n您的問題：%s\n\n我正在處理您的請求...",
		provider.Name(), message)

	a.addAssistantMessage(response, provider.Name())
}

// 快速動作方法
func (a *AIAssistant) analyzeCode() {
	a.currentMessage.Set("請分析當前專案的程式碼結構和品質")
	a.sendMessage()
}

func (a *AIAssistant) projectAssist() {
	a.currentMessage.Set("請提供專案開發的建議和最佳實踐")
	a.sendMessage()
}

func (a *AIAssistant) debugAssist() {
	a.currentMessage.Set("請協助分析和解決程式錯誤")
	a.sendMessage()
}

func (a *AIAssistant) clearConversation() {
	a.conversation = []ConversationMessage{}
	a.chatHistory.Refresh()
	a.addWelcomeMessage()
}

// 設定相關方法
func (a *AIAssistant) onProviderChanged(provider string) {
	a.config.DefaultProvider = provider
	a.updateModelList()
}

func (a *AIAssistant) onModelChanged(model string) {
	// 更新選定的模型
}

func (a *AIAssistant) updateModelList() {
	if provider := a.providers[a.config.DefaultProvider]; provider != nil {
		models := provider.GetModels()
		a.modelSelect.Options = models
		if len(models) > 0 {
			a.modelSelect.SetSelected(models[0])
		}
	}
}

func (a *AIAssistant) saveSettings() {
	// 儲存設定到檔案
}

func (a *AIAssistant) resetSettings() {
	// 重設為預設設定
}

func (a *AIAssistant) refreshContext() {
	// 重新載入專案上下文
}

func (a *AIAssistant) saveConversation() {
	// 儲存對話到檔案
}

func (a *AIAssistant) loadConversation() {
	// 從檔案載入對話
}

func (a *AIAssistant) showAdvancedSettings() {
	// 顯示進階設定視窗
}

// Content 回傳 UI 內容
func (a *AIAssistant) Content() fyne.CanvasObject {
	return a.content
}

// AI 提供者實現（模擬）

// ClaudeProvider Claude AI 提供者
type ClaudeProvider struct {
	config *AIConfig
}

func NewClaudeProvider(config *AIConfig) *ClaudeProvider {
	return &ClaudeProvider{config: config}
}

func (c *ClaudeProvider) Name() string { return "Claude AI" }

func (c *ClaudeProvider) SendMessage(ctx context.Context, message string) (*AIResponse, error) {
	// 實際實現應該調用 Claude API
	return &AIResponse{
		Content:  "Claude AI 回應: " + message,
		Provider: "claude",
		Model:    c.config.ClaudeModel,
	}, nil
}

func (c *ClaudeProvider) SendMessageWithContext(ctx context.Context, message string, context []string) (*AIResponse, error) {
	return c.SendMessage(ctx, message)
}

func (c *ClaudeProvider) GetModels() []string {
	return []string{"claude-3-sonnet-20240229", "claude-3-haiku-20240307"}
}

func (c *ClaudeProvider) IsAvailable() bool {
	return c.config.ClaudeAPIKey != ""
}

// GeminiProvider Gemini AI 提供者
type GeminiProvider struct {
	config *AIConfig
}

func NewGeminiProvider(config *AIConfig) *GeminiProvider {
	return &GeminiProvider{config: config}
}

func (g *GeminiProvider) Name() string { return "Gemini AI" }

func (g *GeminiProvider) SendMessage(ctx context.Context, message string) (*AIResponse, error) {
	return &AIResponse{
		Content:  "Gemini AI 回應: " + message,
		Provider: "gemini",
		Model:    g.config.GeminiModel,
	}, nil
}

func (g *GeminiProvider) SendMessageWithContext(ctx context.Context, message string, context []string) (*AIResponse, error) {
	return g.SendMessage(ctx, message)
}

func (g *GeminiProvider) GetModels() []string {
	return []string{"gemini-pro", "gemini-pro-vision"}
}

func (g *GeminiProvider) IsAvailable() bool {
	return g.config.GeminiAPIKey != ""
}

// OpenAIProvider OpenAI 提供者
type OpenAIProvider struct {
	config *AIConfig
}

func NewOpenAIProvider(config *AIConfig) *OpenAIProvider {
	return &OpenAIProvider{config: config}
}

func (o *OpenAIProvider) Name() string { return "OpenAI" }

func (o *OpenAIProvider) SendMessage(ctx context.Context, message string) (*AIResponse, error) {
	return &AIResponse{
		Content:  "OpenAI 回應: " + message,
		Provider: "openai",
		Model:    o.config.OpenAIModel,
	}, nil
}

func (o *OpenAIProvider) SendMessageWithContext(ctx context.Context, message string, context []string) (*AIResponse, error) {
	return o.SendMessage(ctx, message)
}

func (o *OpenAIProvider) GetModels() []string {
	return []string{"gpt-4", "gpt-3.5-turbo"}
}

func (o *OpenAIProvider) IsAvailable() bool {
	return o.config.OpenAIAPIKey != ""
}

// 開發助手工具

// CodeAnalyzer 程式碼分析器
type CodeAnalyzer struct{}

func NewCodeAnalyzer() *CodeAnalyzer {
	return &CodeAnalyzer{}
}

// ProjectHelper 專案助手
type ProjectHelper struct{}

func NewProjectHelper() *ProjectHelper {
	return &ProjectHelper{}
}

// DebugAssistant 除錯助手
type DebugAssistant struct{}

func NewDebugAssistant() *DebugAssistant {
	return &DebugAssistant{}
}
