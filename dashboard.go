// dashboard.go - 儀表板模組
package main

import (
	"fmt"
	"runtime"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

// Dashboard 儀表板結構
type Dashboard struct {
	app     *Application
	content fyne.CanvasObject

	// 系統資訊卡片
	systemCard    *Card
	projectCard   *Card
	quickActions  *Card
	recentTasks   *Card
	aiStatus      *Card
	resourceUsage *Card

	// 資料
	systemInfo    *SystemInfo
	projectInfo   *ProjectInfo
	recentActions []RecentAction
}

// SystemInfo 系統資訊
type SystemInfo struct {
	OS           string
	Architecture string
	GoVersion    string
	CPUCores     int
	Memory       string
	Uptime       time.Duration
}

// ProjectInfo 專案資訊
type ProjectInfo struct {
	CurrentProject string
	GitBranch      string
	GitStatus      string
	LastCommit     string
	FilesChanged   int
}

// RecentAction 最近動作
type RecentAction struct {
	Type        string // "task", "query", "k8s", "ai"
	Description string
	Timestamp   time.Time
	Status      string // "success", "failed", "running"
}

// NewDashboard 建立新的儀表板
func NewDashboard(app *Application) *Dashboard {
	d := &Dashboard{
		app:           app,
		recentActions: []RecentAction{},
	}

	// 收集系統資訊
	d.collectSystemInfo()
	d.collectProjectInfo()

	// 建立 UI
	d.buildUI()

	// 開始定期更新
	go d.startPeriodicUpdate()

	return d
}

// collectSystemInfo 收集系統資訊
func (d *Dashboard) collectSystemInfo() {
	d.systemInfo = &SystemInfo{
		OS:           runtime.GOOS,
		Architecture: runtime.GOARCH,
		GoVersion:    runtime.Version(),
		CPUCores:     runtime.NumCPU(),
		Memory:       "8GB",                                      // 簡化實現
		Uptime:       time.Since(time.Now().Add(-2 * time.Hour)), // 模擬
	}
}

// collectProjectInfo 收集專案資訊
func (d *Dashboard) collectProjectInfo() {
	d.projectInfo = &ProjectInfo{
		CurrentProject: "assistant-go",
		GitBranch:      "main",
		GitStatus:      "clean",
		LastCommit:     "feat: 添加 AI 助手功能",
		FilesChanged:   0,
	}
}

// buildUI 建立使用者介面
func (d *Dashboard) buildUI() {
	// 建立各個卡片
	d.createSystemCard()
	d.createProjectCard()
	d.createQuickActionsCard()
	d.createRecentTasksCard()
	d.createAIStatusCard()
	d.createResourceUsageCard()

	// 組合佈局 - 使用響應式網格，添加間距
	topRow := container.NewGridWithColumns(3,
		d.systemCard,
		d.projectCard,
		d.aiStatus,
	)

	middleRow := container.NewGridWithColumns(3,
		d.quickActions,
		d.recentTasks,
		d.resourceUsage,
	)

	// 歡迎標題 - 駭客風格
	welcomeLabel := widget.NewLabelWithStyle(
		">>> SYSTEM ONLINE - WELCOME BACK, KOOPA <<<",
		fyne.TextAlignCenter,
		fyne.TextStyle{Bold: true, Monospace: true},
	)

	timeLabel := widget.NewLabelWithStyle(
		fmt.Sprintf("[%s] CYBER TERMINAL ACTIVE", time.Now().Format("2006-01-02 15:04:05")),
		fyne.TextAlignCenter,
		fyne.TextStyle{Monospace: true},
	)

	header := container.NewVBox(
		welcomeLabel,
		timeLabel,
		widget.NewSeparator(),
	)

	d.content = container.NewVBox(
		header,
		topRow,
		middleRow,
	)
}

// createSystemCard 建立系統資訊卡片
func (d *Dashboard) createSystemCard() {
	content := container.NewVBox(
		container.NewHBox(
			widget.NewIcon(theme.ComputerIcon()),
			widget.NewLabelWithStyle("🖥️ SYSTEM STATUS", fyne.TextAlignLeading, fyne.TextStyle{Bold: true, Monospace: true}),
		),
		widget.NewSeparator(),
		widget.NewLabel(fmt.Sprintf("作業系統: %s", d.systemInfo.OS)),
		widget.NewLabel(fmt.Sprintf("架構: %s", d.systemInfo.Architecture)),
		widget.NewLabel(fmt.Sprintf("Go 版本: %s", d.systemInfo.GoVersion)),
		widget.NewLabel(fmt.Sprintf("CPU 核心: %d", d.systemInfo.CPUCores)),
		widget.NewLabel(fmt.Sprintf("記憶體: %s", d.systemInfo.Memory)),
	)

	d.systemCard = NewCard("", content)
}

// createProjectCard 建立專案資訊卡片
func (d *Dashboard) createProjectCard() {
	content := container.NewVBox(
		container.NewHBox(
			widget.NewIcon(theme.FolderIcon()),
			widget.NewLabelWithStyle("專案狀態", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		),
		widget.NewSeparator(),
		widget.NewLabel(fmt.Sprintf("專案: %s", d.projectInfo.CurrentProject)),
		widget.NewLabel(fmt.Sprintf("分支: %s", d.projectInfo.GitBranch)),
		widget.NewLabel(fmt.Sprintf("狀態: %s", d.projectInfo.GitStatus)),
		widget.NewLabel(fmt.Sprintf("最後提交: %s", d.projectInfo.LastCommit)),
		container.NewHBox(
			widget.NewButtonWithIcon("Git 狀態", theme.InfoIcon(), func() {
				d.app.showTaskRunner()
			}),
		),
	)

	d.projectCard = NewCard("", content)
}

// createQuickActionsCard 建立快速動作卡片
func (d *Dashboard) createQuickActionsCard() {
	content := container.NewVBox(
		container.NewHBox(
			widget.NewIcon(theme.MediaFastForwardIcon()),
			widget.NewLabelWithStyle("快速動作", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		),
		widget.NewSeparator(),
		widget.NewButtonWithIcon("執行測試", theme.MediaPlayIcon(), func() {
			d.addRecentAction("task", "執行測試", "running")
		}),
		widget.NewButtonWithIcon("建置專案", theme.DocumentCreateIcon(), func() {
			d.addRecentAction("task", "建置專案", "running")
		}),
		widget.NewButtonWithIcon("查看 K8s", theme.ComputerIcon(), func() {
			d.app.showK8sManager()
		}),
		widget.NewButtonWithIcon("AI 協助", theme.HelpIcon(), func() {
			d.app.showAIAssistant()
		}),
	)

	d.quickActions = NewCard("", content)
}

// createRecentTasksCard 建立最近任務卡片
func (d *Dashboard) createRecentTasksCard() {
	taskList := widget.NewList(
		func() int {
			return len(d.recentActions)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewIcon(theme.InfoIcon()),
				widget.NewLabel("任務"),
				widget.NewLabel("時間"),
			)
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			if id >= len(d.recentActions) {
				return
			}
			action := d.recentActions[id]
			container := item.(*fyne.Container)

			// 設定圖示
			icon := container.Objects[0].(*widget.Icon)
			switch action.Type {
			case "task":
				icon.SetResource(theme.MediaPlayIcon())
			case "query":
				icon.SetResource(theme.StorageIcon())
			case "k8s":
				icon.SetResource(theme.ComputerIcon())
			case "ai":
				icon.SetResource(theme.HelpIcon())
			}

			// 設定文字
			container.Objects[1].(*widget.Label).SetText(action.Description)
			container.Objects[2].(*widget.Label).SetText(action.Timestamp.Format("15:04"))
		},
	)

	content := container.NewVBox(
		container.NewHBox(
			widget.NewIcon(theme.HistoryIcon()),
			widget.NewLabelWithStyle("最近活動", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		),
		widget.NewSeparator(),
		taskList,
	)

	d.recentTasks = NewCard("", content)
}

// createAIStatusCard 建立 AI 狀態卡片
func (d *Dashboard) createAIStatusCard() {
	content := container.NewVBox(
		container.NewHBox(
			widget.NewIcon(theme.HelpIcon()),
			widget.NewLabelWithStyle("AI 助手狀態", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		),
		widget.NewSeparator(),
		container.NewHBox(
			widget.NewIcon(theme.ConfirmIcon()),
			widget.NewLabel("Claude AI: 已連接"),
		),
		container.NewHBox(
			widget.NewIcon(theme.ConfirmIcon()),
			widget.NewLabel("Gemini AI: 已連接"),
		),
		container.NewHBox(
			widget.NewIcon(theme.InfoIcon()),
			widget.NewLabel("今日查詢: 12 次"),
		),
		widget.NewButtonWithIcon("開始對話", theme.MailSendIcon(), func() {
			d.app.showAIAssistant()
		}),
	)

	d.aiStatus = NewCard("", content)
}

// createResourceUsageCard 建立資源使用卡片
func (d *Dashboard) createResourceUsageCard() {
	cpuProgress := widget.NewProgressBar()
	cpuProgress.SetValue(0.35) // 35% CPU 使用率

	memProgress := widget.NewProgressBar()
	memProgress.SetValue(0.68) // 68% 記憶體使用率

	content := container.NewVBox(
		container.NewHBox(
			widget.NewIcon(theme.SettingsIcon()),
			widget.NewLabelWithStyle("資源使用", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		),
		widget.NewSeparator(),
		widget.NewLabel("CPU 使用率: 35%"),
		cpuProgress,
		widget.NewLabel("記憶體使用率: 68%"),
		memProgress,
		widget.NewLabel("磁碟空間: 256GB 可用"),
	)

	d.resourceUsage = NewCard("", content)
}

// addRecentAction 添加最近動作
func (d *Dashboard) addRecentAction(actionType, description, status string) {
	action := RecentAction{
		Type:        actionType,
		Description: description,
		Timestamp:   time.Now(),
		Status:      status,
	}

	d.recentActions = append([]RecentAction{action}, d.recentActions...)
	if len(d.recentActions) > 10 {
		d.recentActions = d.recentActions[:10]
	}

	// 更新 UI
	if d.recentTasks != nil {
		// 這裡需要重新整理列表
	}
}

// startPeriodicUpdate 開始定期更新
func (d *Dashboard) startPeriodicUpdate() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		// 更新系統資訊
		d.collectSystemInfo()
		d.collectProjectInfo()
		// 這裡可以更新 UI 元件
	}
}

// Content 回傳儀表板內容
func (d *Dashboard) Content() fyne.CanvasObject {
	return d.content
}
