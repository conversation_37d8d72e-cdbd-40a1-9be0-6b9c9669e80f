// database_manager.go - PostgreSQL 資料庫管理器模組
package main

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/widget"

	_ "github.com/lib/pq"
)

// DatabaseConfig 儲存資料庫連線配置
type DatabaseConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	Database string
	SSLMode  string
}

// SavedQuery 代表一個儲存的查詢
type SavedQuery struct {
	Name        string
	Query       string
	Description string
	Category    string
	Parameters  []string
}

// DatabaseManager 管理 PostgreSQL 資料庫操作
type DatabaseManager struct {
	config       *DatabaseConfig
	db           *sql.DB
	savedQueries []SavedQuery

	// UI 元件
	content        fyne.CanvasObject
	queryEditor    *widget.Entry
	resultTable    *widget.Table
	queryHistory   *widget.List
	savedQueryList *widget.List
	perfMonitor    *PerformanceMonitor

	// 資料綁定
	currentQuery  binding.String
	queryResults  [][]string
	resultColumns []string

	// AI 整合準備
	queryOptimizer *QueryOptimizer
}

// NewDatabaseManager 建立新的資料庫管理器
func NewDatabaseManager(config *DatabaseConfig) *DatabaseManager {
	m := &DatabaseManager{
		config:       config,
		currentQuery: binding.NewString(),
		savedQueries: loadSavedQueries(), // 從設定檔載入儲存的查詢
	}

	// 初始化資料庫連線
	if err := m.connect(); err != nil {
		// 錯誤處理 - 稍後會在 UI 中顯示
		fmt.Printf("資料庫連線失敗: %v\n", err)
	}

	// 初始化查詢優化器（為未來 AI 整合做準備）
	m.queryOptimizer = NewQueryOptimizer()

	// 建立 UI
	m.buildUI()

	// 開始效能監控
	go m.startPerformanceMonitoring()

	return m
}

// connect 建立資料庫連線
func (m *DatabaseManager) connect() error {
	psqlInfo := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		m.config.Host, m.config.Port, m.config.User, m.config.Password, m.config.Database, m.config.SSLMode)

	db, err := sql.Open("postgres", psqlInfo)
	if err != nil {
		return fmt.Errorf("無法開啟資料庫連線: %v", err)
	}

	// 測試連線
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		return fmt.Errorf("無法連接到資料庫: %v", err)
	}

	m.db = db
	return nil
}

// buildUI 建立使用者介面
func (m *DatabaseManager) buildUI() {
	// 建立查詢編輯器區域
	querySection := m.createQuerySection()

	// 建立結果顯示區域
	resultSection := m.createResultSection()

	// 建立側邊欄（儲存的查詢和歷史記錄）
	sidebar := m.createSidebar()

	// 建立效能監控面板
	m.perfMonitor = NewPerformanceMonitor(m.db)

	// 組合主要佈局
	mainContent := container.NewVSplit(
		querySection,
		container.NewHSplit(
			resultSection,
			NewCard("效能監控", m.perfMonitor),
		),
	)
	mainContent.SetOffset(0.3)

	// 最終佈局 - 添加滾動支援
	m.content = container.NewHSplit(
		container.NewScroll(sidebar),     // 側邊欄添加滾動
		container.NewScroll(mainContent), // 主內容添加滾動
	)
}

// createQuerySection 建立查詢編輯區域
func (m *DatabaseManager) createQuerySection() *fyne.Container {
	// 查詢編輯器
	m.queryEditor = widget.NewMultiLineEntry()
	m.queryEditor.SetPlaceHolder("輸入 SQL 查詢...")
	m.queryEditor.Bind(m.currentQuery)

	// 工具列
	toolbar := container.NewHBox(
		widget.NewButton("執行 (F5)", m.executeQuery),
		widget.NewButton("格式化", m.formatQuery),
		widget.NewButton("解釋計畫", m.explainQuery),
		widget.NewButton("儲存查詢", m.saveQuery),
		widget.NewButton("清除", func() {
			m.currentQuery.Set("")
		}),
	)

	// 智能提示區域（準備未來 AI 整合）
	suggestionLabel := widget.NewLabel("💡 提示: 使用 Ctrl+Space 顯示智能建議")

	return container.NewBorder(
		container.NewVBox(
			widget.NewLabelWithStyle("查詢編輯器", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
			toolbar,
		),
		suggestionLabel,
		nil,
		nil,
		container.NewScroll(m.queryEditor),
	)
}

// createResultSection 建立結果顯示區域
func (m *DatabaseManager) createResultSection() *fyne.Container {
	// 建立結果表格
	m.resultTable = widget.NewTable(
		func() (int, int) {
			if len(m.queryResults) == 0 {
				return 0, 0
			}
			return len(m.queryResults), len(m.queryResults[0])
		},
		func() fyne.CanvasObject {
			return widget.NewLabel("載入中...")
		},
		func(id widget.TableCellID, cell fyne.CanvasObject) {
			label := cell.(*widget.Label)
			if id.Row < len(m.queryResults) && id.Col < len(m.queryResults[id.Row]) {
				label.SetText(m.queryResults[id.Row][id.Col])
			}
		},
	)

	// 設定表格標題
	m.resultTable.ShowHeaderRow = true
	m.resultTable.CreateHeader = func() fyne.CanvasObject {
		if len(m.resultColumns) == 0 {
			return widget.NewLabel("無結果")
		}

		headers := make([]fyne.CanvasObject, len(m.resultColumns))
		for i, col := range m.resultColumns {
			headers[i] = widget.NewLabelWithStyle(col, fyne.TextAlignLeading, fyne.TextStyle{Bold: true})
		}
		return container.NewHBox(headers...)
	}

	// 結果統計
	statsLabel := widget.NewLabel("準備就緒")

	return container.NewBorder(
		widget.NewLabelWithStyle("查詢結果", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		statsLabel,
		nil,
		nil,
		m.resultTable,
	)
}

// createSidebar 建立側邊欄
func (m *DatabaseManager) createSidebar() *fyne.Container {
	// 儲存的查詢列表
	m.savedQueryList = widget.NewList(
		func() int { return len(m.savedQueries) },
		func() fyne.CanvasObject {
			return container.NewVBox(
				widget.NewLabel("查詢名稱"),
				widget.NewLabel("描述"),
			)
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			container := item.(*fyne.Container)
			labels := container.Objects

			labels[0].(*widget.Label).SetText(m.savedQueries[id].Name)
			labels[1].(*widget.Label).SetText(m.savedQueries[id].Description)
		},
	)

	// 點擊載入查詢
	m.savedQueryList.OnSelected = func(id widget.ListItemID) {
		m.currentQuery.Set(m.savedQueries[id].Query)
	}

	// 查詢歷史
	queryHistory := []string{} // 實際應該從某處載入
	m.queryHistory = widget.NewList(
		func() int { return len(queryHistory) },
		func() fyne.CanvasObject {
			return widget.NewLabel("歷史查詢")
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			item.(*widget.Label).SetText(queryHistory[id])
		},
	)

	// 分頁
	tabs := container.NewAppTabs(
		container.NewTabItem("常用查詢", m.savedQueryList),
		container.NewTabItem("歷史記錄", m.queryHistory),
	)

	return container.NewBorder(
		widget.NewLabel("查詢工具"),
		nil,
		nil,
		nil,
		tabs,
	)
}

// executeQuery 執行查詢
func (m *DatabaseManager) executeQuery() {
	if m.db == nil {
		// 簡化錯誤顯示
		m.showError(fmt.Errorf("資料庫未連接"))
		return
	}

	query, _ := m.currentQuery.Get()
	if strings.TrimSpace(query) == "" {
		return
	}

	// 記錄開始時間（用於效能監控）
	startTime := time.Now()

	// 執行查詢
	rows, err := m.db.Query(query)
	if err != nil {
		// 顯示錯誤
		m.showError(err)
		return
	}
	defer rows.Close()

	// 獲取欄位資訊
	columns, err := rows.Columns()
	if err != nil {
		m.showError(err)
		return
	}
	m.resultColumns = columns

	// 讀取結果
	m.queryResults = [][]string{}
	values := make([]interface{}, len(columns))
	valuePtrs := make([]interface{}, len(columns))

	for rows.Next() {
		for i := range columns {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			m.showError(err)
			return
		}

		row := make([]string, len(columns))
		for i, v := range values {
			if v != nil {
				row[i] = fmt.Sprintf("%v", v)
			} else {
				row[i] = "NULL"
			}
		}
		m.queryResults = append(m.queryResults, row)
	}

	// 更新表格
	m.resultTable.Refresh()

	// 記錄查詢時間
	duration := time.Since(startTime)
	m.perfMonitor.RecordQuery(query, duration, len(m.queryResults))
}

// formatQuery 格式化 SQL 查詢
func (m *DatabaseManager) formatQuery() {
	query, _ := m.currentQuery.Get()
	formatted := m.queryOptimizer.FormatSQL(query)
	m.currentQuery.Set(formatted)
}

// explainQuery 顯示查詢執行計畫
func (m *DatabaseManager) explainQuery() {
	query, _ := m.currentQuery.Get()
	explainQuery := fmt.Sprintf("EXPLAIN ANALYZE %s", query)
	m.currentQuery.Set(explainQuery)
	m.executeQuery()
}

// saveQuery 儲存查詢
func (m *DatabaseManager) saveQuery() {
	query, _ := m.currentQuery.Get()

	// 建立對話框讓使用者輸入查詢資訊
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("查詢名稱")

	descEntry := widget.NewEntry()
	descEntry.SetPlaceHolder("查詢描述")

	categoryEntry := widget.NewEntry()
	categoryEntry.SetPlaceHolder("分類")

	_ = container.NewVBox(
		widget.NewLabel("儲存查詢"),
		nameEntry,
		descEntry,
		categoryEntry,
	)

	// 創建對話框 - 簡化實現
	// TODO: 實現完整的對話框功能
	if nameEntry.Text != "" {
		newQuery := SavedQuery{
			Name:        nameEntry.Text,
			Query:       query,
			Description: descEntry.Text,
			Category:    categoryEntry.Text,
		}
		m.savedQueries = append(m.savedQueries, newQuery)
		m.savedQueryList.Refresh()

		// 儲存到檔案
		saveSavedQueries(m.savedQueries)
	}
}

// showError 顯示錯誤訊息
func (m *DatabaseManager) showError(err error) {
	// 在結果區域顯示錯誤
	m.queryResults = [][]string{{"錯誤: " + err.Error()}}
	m.resultColumns = []string{"訊息"}
	m.resultTable.Refresh()
}

// startPerformanceMonitoring 開始效能監控
func (m *DatabaseManager) startPerformanceMonitoring() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		if m.db != nil {
			m.perfMonitor.UpdateMetrics()
		}
	}
}

// Content 回傳 UI 內容
func (m *DatabaseManager) Content() fyne.CanvasObject {
	return m.content
}

// 輔助函數
func loadSavedQueries() []SavedQuery {
	// 預設的常用查詢
	return []SavedQuery{
		{
			Name:        "顯示所有資料表",
			Query:       "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'",
			Description: "列出所有公開的資料表",
			Category:    "系統",
		},
		{
			Name:        "資料表大小",
			Query:       "SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size FROM pg_tables ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC",
			Description: "顯示所有資料表的大小",
			Category:    "效能",
		},
		{
			Name:        "活動連線",
			Query:       "SELECT pid, usename, application_name, client_addr, backend_start, state FROM pg_stat_activity",
			Description: "顯示目前的資料庫連線",
			Category:    "監控",
		},
	}
}

func saveSavedQueries(queries []SavedQuery) {
	// 實作儲存到檔案的邏輯
}

// QueryOptimizer 查詢優化器（為 AI 整合準備）
type QueryOptimizer struct {
	// 未來會整合 AI 模型來提供查詢建議
}

func NewQueryOptimizer() *QueryOptimizer {
	return &QueryOptimizer{}
}

func (q *QueryOptimizer) FormatSQL(sql string) string {
	// 簡單的 SQL 格式化實作
	// 未來可以使用更複雜的解析器
	formatted := strings.ToUpper(sql)
	formatted = strings.ReplaceAll(formatted, "SELECT", "SELECT\n  ")
	formatted = strings.ReplaceAll(formatted, "FROM", "\nFROM")
	formatted = strings.ReplaceAll(formatted, "WHERE", "\nWHERE")
	formatted = strings.ReplaceAll(formatted, "AND", "\n  AND")
	formatted = strings.ReplaceAll(formatted, "OR", "\n  OR")
	return formatted
}

// PerformanceMonitor 效能監控器
type PerformanceMonitor struct {
	widget.BaseWidget
	db        *sql.DB
	container *fyne.Container
	metrics   *DatabaseMetrics
}

type DatabaseMetrics struct {
	ActiveConnections int
	DatabaseSize      string
	CacheHitRatio     float64
	SlowQueries       int
}

func NewPerformanceMonitor(db *sql.DB) *PerformanceMonitor {
	p := &PerformanceMonitor{
		db:      db,
		metrics: &DatabaseMetrics{},
	}
	p.ExtendBaseWidget(p)

	// 建立監控 UI
	p.container = container.NewVBox(
		widget.NewLabel("連線數: 0"),
		widget.NewLabel("資料庫大小: --"),
		widget.NewLabel("快取命中率: --"),
		widget.NewLabel("慢查詢: 0"),
		widget.NewSeparator(),
		widget.NewButton("詳細報告", p.showDetailedReport),
	)

	return p
}

func (p *PerformanceMonitor) CreateRenderer() fyne.WidgetRenderer {
	return widget.NewSimpleRenderer(p.container)
}

func (p *PerformanceMonitor) UpdateMetrics() {
	// 更新效能指標
	// 這裡應該執行實際的查詢來獲取指標
}

func (p *PerformanceMonitor) RecordQuery(query string, duration time.Duration, rowCount int) {
	// 記錄查詢效能
}

func (p *PerformanceMonitor) showDetailedReport() {
	// 顯示詳細的效能報告
}
