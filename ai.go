// ai.go - AI 助手模組，整合 Claude AI、Gemini AI 等
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

// AIConfig AI 助手配置
type AIConfig struct {
	// Claude AI 配置
	ClaudeAPIKey    string
	ClaudeModel     string // claude-3-sonnet-20240229, claude-3-haiku-20240307
	ClaudeBaseURL   string

	// Gemini AI 配置
	GeminiAPIKey  string
	GeminiModel   string // gemini-pro, gemini-pro-vision
	GeminiBaseURL string

	// OpenAI 配置 (備用)
	OpenAIAPIKey  string
	OpenAIModel   string
	OpenAIBaseURL string

	// LangChain 配置
	LangChainEnabled bool
	LangChainConfig  map[string]interface{}

	// 一般設定
	DefaultProvider string // "claude", "gemini", "openai"
	MaxTokens       int
	Temperature     float32
	SystemPrompt    string
}

// AIProvider AI 提供者介面
type AIProvider interface {
	Name() string
	SendMessage(ctx context.Context, message string) (*AIResponse, error)
	SendMessageWithContext(ctx context.Context, message string, context []string) (*AIResponse, error)
	GetModels() []string
	IsAvailable() bool
}

// AIResponse AI 回應
type AIResponse struct {
	Content      string
	Provider     string
	Model        string
	TokensUsed   int
	ResponseTime time.Duration
	Error        error
}

// ConversationMessage 對話訊息
type ConversationMessage struct {
	Role      string    // "user", "assistant", "system"
	Content   string
	Timestamp time.Time
	Provider  string
	Metadata  map[string]interface{}
}

// AIAssistant AI 助手主要結構
type AIAssistant struct {
	config       *AIConfig
	providers    map[string]AIProvider
	conversation []ConversationMessage

	// UI 元件
	content         fyne.CanvasObject
	chatHistory     *widget.List
	messageInput    *widget.Entry
	sendButton      *widget.Button
	providerSelect  *widget.Select
	modelSelect     *widget.Select
	settingsPanel   *fyne.Container
	contextPanel    *fyne.Container

	// 資料綁定
	currentMessage binding.String
	isProcessing   binding.Bool

	// 開發助手功能
	codeAnalyzer    *CodeAnalyzer
	projectHelper   *ProjectHelper
	debugAssistant  *DebugAssistant
}

// NewAIAssistant 建立新的 AI 助手
func NewAIAssistant(config *AIConfig) *AIAssistant {
	a := &AIAssistant{
		config:         config,
		providers:      make(map[string]AIProvider),
		conversation:   []ConversationMessage{},
		currentMessage: binding.NewString(),
		isProcessing:   binding.NewBool(),
	}

	// 初始化 AI 提供者
	a.initializeProviders()

	// 初始化開發助手功能
	a.codeAnalyzer = NewCodeAnalyzer()
	a.projectHelper = NewProjectHelper()
	a.debugAssistant = NewDebugAssistant()

	// 建立 UI
	a.buildUI()

	// 添加歡迎訊息
	a.addWelcomeMessage()

	return a
}

// initializeProviders 初始化 AI 提供者
func (a *AIAssistant) initializeProviders() {
	// Claude AI
	if a.config.ClaudeAPIKey != "" {
		a.providers["claude"] = NewClaudeProvider(a.config)
	}

	// Gemini AI
	if a.config.GeminiAPIKey != "" {
		a.providers["gemini"] = NewGeminiProvider(a.config)
	}

	// OpenAI (備用)
	if a.config.OpenAIAPIKey != "" {
		a.providers["openai"] = NewOpenAIProvider(a.config)
	}
}

// buildUI 建立使用者介面
func (a *AIAssistant) buildUI() {
	// 建立聊天歷史
	a.createChatHistory()

	// 建立輸入區域
	inputArea := a.createInputArea()

	// 建立設定面板
	a.createSettingsPanel()

	// 建立上下文面板
	a.createContextPanel()

	// 建立工具列
	toolbar := a.createToolbar()

	// 主要聊天區域
	chatArea := container.NewBorder(
		toolbar,
		inputArea,
		nil,
		nil,
		a.chatHistory,
	)

	// 側邊面板
	sidePanel := container.NewAppTabs(
		container.NewTabItem("設定", a.settingsPanel),
		container.NewTabItem("上下文", a.contextPanel),
		container.NewTabItem("開發助手", a.createDevAssistantPanel()),
	)

	// 組合主要佈局
	a.content = container.NewHSplit(
		chatArea,
		sidePanel,
	)
}

// createChatHistory 建立聊天歷史
func (a *AIAssistant) createChatHistory() {
	a.chatHistory = widget.NewList(
		func() int {
			return len(a.conversation)
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				container.NewHBox(
					widget.NewIcon(theme.AccountIcon()),
					widget.NewLabelWithStyle("角色", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
					widget.NewLabel("時間"),
				),
				widget.NewRichTextFromMarkdown("訊息內容"),
			)
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			if id >= len(a.conversation) {
				return
			}

			msg := a.conversation[id]
			container := item.(*fyne.Container)

			// 更新標題列
			header := container.Objects[0].(*fyne.Container)
			icon := header.Objects[0].(*widget.Icon)
			roleLabel := header.Objects[1].(*widget.Label)
			timeLabel := header.Objects[2].(*widget.Label)

			// 根據角色設定圖示和顏色
			switch msg.Role {
			case "user":
				icon.SetResource(theme.AccountIcon())
				roleLabel.SetText("您")
			case "assistant":
				icon.SetResource(theme.ComputerIcon())
				roleLabel.SetText("AI 助手")
			case "system":
				icon.SetResource(theme.InfoIcon())
				roleLabel.SetText("系統")
			}

			timeLabel.SetText(msg.Timestamp.Format("15:04"))

			// 更新訊息內容
			content := container.Objects[1].(*widget.RichText)
			content.ParseMarkdown(msg.Content)
		},
	)
}

// createInputArea 建立輸入區域
func (a *AIAssistant) createInputArea() *fyne.Container {
	// 訊息輸入框
	a.messageInput = widget.NewMultiLineEntry()
	a.messageInput.Bind(a.currentMessage)
	a.messageInput.SetPlaceHolder("輸入您的問題或請求...")
	a.messageInput.Wrapping = fyne.TextWrapWord

	// 發送按鈕
	a.sendButton = widget.NewButtonWithIcon("發送", theme.MailSendIcon(), a.sendMessage)

	// 快速動作按鈕
	quickActions := container.NewHBox(
		widget.NewButtonWithIcon("程式碼分析", theme.DocumentIcon(), a.analyzeCode),
		widget.NewButtonWithIcon("專案協助", theme.FolderIcon(), a.projectAssist),
		widget.NewButtonWithIcon("除錯協助", theme.BrokenImageIcon(), a.debugAssist),
		widget.NewButtonWithIcon("清空對話", theme.DeleteIcon(), a.clearConversation),
	)

	// 組合輸入區域
	return container.NewBorder(
		quickActions,
		container.NewHBox(
			widget.NewLabel(""),
			a.sendButton,
		),
		nil,
		nil,
		container.NewScroll(a.messageInput),
	)
}

// createSettingsPanel 建立設定面板
func (a *AIAssistant) createSettingsPanel() {
	// 提供者選擇
	providers := []string{}
	for name := range a.providers {
		providers = append(providers, name)
	}
	a.providerSelect = widget.NewSelect(providers, a.onProviderChanged)
	a.providerSelect.SetSelected(a.config.DefaultProvider)

	// 模型選擇
	a.modelSelect = widget.NewSelect([]string{}, a.onModelChanged)
	a.updateModelList()

	// 設定項目
	a.settingsPanel = container.NewVBox(
		widget.NewLabelWithStyle("AI 設定", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		widget.NewSeparator(),
		
		widget.NewLabel("AI 提供者:"),
		a.providerSelect,
		
		widget.NewLabel("模型:"),
		a.modelSelect,
		
		widget.NewLabel("系統提示:"),
		widget.NewMultiLineEntry(), // TODO: 綁定到系統提示
		
		widget.NewSeparator(),
		widget.NewButton("儲存設定", a.saveSettings),
		widget.NewButton("重設設定", a.resetSettings),
	)
}

// Content 回傳 UI 內容
func (a *AIAssistant) Content() fyne.CanvasObject {
	return a.content
}

// 其他方法將在下一個檔案中繼續實現...
