// k8s_manager.go - Kubernetes 資源管理模組
package main

import (
	"context"
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/widget"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

// K8sConfig 儲存 K8s 連線配置
type K8sConfig struct {
	KubeConfigPath string
	CurrentContext string
	Namespace      string
}

// K8sManager 管理 Kubernetes 資源
type K8sManager struct {
	config    *K8sConfig
	client    *kubernetes.Clientset
	namespace binding.String

	// UI 元件
	content       fyne.CanvasObject
	podTable      *widget.Table
	resourceChart *ResourceChart
	quotaDisplay  *QuotaDisplay
	logViewer     *widget.Entry

	// 資料綁定
	pods     []v1.Pod
	podData  binding.ExternalUntypedList
	selected int
}

// NewK8sManager 建立新的 K8s 管理器
func NewK8sManager(config *K8sConfig) *K8sManager {
	m := &K8sManager{
		config:    config,
		namespace: binding.NewString(),
		selected:  -1,
	}

	// 初始化 K8s 客戶端
	m.initClient()

	// 設定初始命名空間
	m.namespace.Set(config.Namespace)

	// 建立 UI
	m.buildUI()

	// 開始監控
	go m.startMonitoring()

	return m
}

// initClient 初始化 Kubernetes 客戶端
func (m *K8sManager) initClient() error {
	// 載入 kubeconfig
	config, err := clientcmd.BuildConfigFromFlags("", m.config.KubeConfigPath)
	if err != nil {
		return fmt.Errorf("無法載入 kubeconfig: %v", err)
	}

	// 建立客戶端
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return fmt.Errorf("無法建立 K8s 客戶端: %v", err)
	}

	m.client = clientset
	return nil
}

// buildUI 建立使用者介面
func (m *K8sManager) buildUI() {
	// 建立命名空間選擇器
	nsSelector := m.createNamespaceSelector()

	// 建立 Pod 表格
	m.createPodTable()

	// 建立資源使用圖表
	m.resourceChart = NewResourceChart()

	// 建立配額顯示
	m.quotaDisplay = NewQuotaDisplay()

	// 建立日誌檢視器
	m.logViewer = widget.NewMultiLineEntry()
	m.logViewer.SetPlaceHolder("選擇一個 Pod 來查看日誌...")

	// 建立分割視圖 - 添加滾動支援
	leftPanel := container.NewBorder(
		nsSelector,
		nil,
		nil,
		nil,
		container.NewScroll(m.podTable), // Pod 表格添加滾動
	)

	rightPanel := container.NewVSplit(
		container.NewHSplit(
			container.NewScroll(m.createCard("資源使用情況", m.resourceChart)), // 資源圖表添加滾動
			container.NewScroll(m.createCard("配額限制", m.quotaDisplay)),     // 配額顯示添加滾動
		),
		m.createCard("Pod 日誌", container.NewScroll(m.logViewer)), // 日誌已有滾動
	)
	rightPanel.SetOffset(0.4)

	// 組合主要內容
	m.content = container.NewHSplit(
		container.NewScroll(leftPanel),  // 左側面板添加滾動
		container.NewScroll(rightPanel), // 右側面板添加滾動
	)
}

// createNamespaceSelector 建立命名空間選擇器
func (m *K8sManager) createNamespaceSelector() *fyne.Container {
	// 命名空間輸入
	nsEntry := widget.NewEntryWithData(m.namespace)
	nsEntry.SetPlaceHolder("輸入命名空間...")

	// 重新載入按鈕
	refreshBtn := widget.NewButton("重新整理", func() {
		m.refreshPods()
	})

	// 快速切換按鈕
	defaultBtn := widget.NewButton("default", func() {
		m.namespace.Set("default")
		m.refreshPods()
	})

	kubeSystemBtn := widget.NewButton("kube-system", func() {
		m.namespace.Set("kube-system")
		m.refreshPods()
	})

	return container.NewBorder(
		nil, nil, nil,
		container.NewHBox(refreshBtn, defaultBtn, kubeSystemBtn),
		nsEntry,
	)
}

// createPodTable 建立 Pod 表格
func (m *K8sManager) createPodTable() {
	// 建立資料綁定
	m.podData = binding.BindUntypedList(&[]interface{}{})

	m.podTable = widget.NewTable(
		func() (int, int) {
			return len(m.pods), 5 // 5 個欄位：名稱,狀態,重啟次數,CPU,記憶體
		},
		func() fyne.CanvasObject {
			return widget.NewLabel("載入中...")
		},
		func(id widget.TableCellID, cell fyne.CanvasObject) {
			label := cell.(*widget.Label)
			if id.Row >= len(m.pods) {
				return
			}

			pod := m.pods[id.Row]
			switch id.Col {
			case 0:
				label.SetText(pod.Name)
			case 1:
				label.SetText(string(pod.Status.Phase))
				// 根據狀態設定顏色
				switch pod.Status.Phase {
				case v1.PodRunning:
					label.TextStyle = fyne.TextStyle{Bold: true}
				case v1.PodFailed:
					label.TextStyle = fyne.TextStyle{Bold: true}
				}
			case 2:
				restarts := 0
				for _, cs := range pod.Status.ContainerStatuses {
					restarts += int(cs.RestartCount)
				}
				label.SetText(fmt.Sprintf("%d", restarts))
			case 3:
				// CPU 使用量 (需要 metrics API)
				label.SetText("--")
			case 4:
				// 記憶體使用量 (需要 metrics API)
				label.SetText("--")
			}
		},
	)

	// 設定列選擇
	m.podTable.OnSelected = func(id widget.TableCellID) {
		m.selected = id.Row
		m.showPodDetails(m.pods[id.Row])
	}

	// 設定標題列
	m.podTable.ShowHeaderRow = true
	m.podTable.CreateHeader = func() fyne.CanvasObject {
		return widget.NewLabelWithStyle("Pod 資訊", fyne.TextAlignLeading, fyne.TextStyle{Bold: true})
	}
}

// refreshPods 重新載入 Pod 列表
func (m *K8sManager) refreshPods() {
	if m.client == nil {
		return
	}

	ns, _ := m.namespace.Get()
	pods, err := m.client.CoreV1().Pods(ns).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		// 錯誤處理
		return
	}

	m.pods = pods.Items
	m.podTable.Refresh()

	// 更新資源圖表
	m.updateResourceChart()
}

// showPodDetails 顯示 Pod 詳細資訊
func (m *K8sManager) showPodDetails(pod v1.Pod) {
	// 獲取 Pod 日誌
	go func() {
		logs := m.getPodLogs(pod.Name, pod.Namespace)
		m.logViewer.SetText(logs)
	}()

	// 更新資源使用情況
	m.updatePodResources(pod)
}

// getPodLogs 獲取 Pod 日誌
func (m *K8sManager) getPodLogs(podName, namespace string) string {
	if m.client == nil {
		return "無法連接到 Kubernetes"
	}

	podLogOptions := &v1.PodLogOptions{
		TailLines: int64Ptr(100),
	}

	req := m.client.CoreV1().Pods(namespace).GetLogs(podName, podLogOptions)
	logs, err := req.Stream(context.TODO())
	if err != nil {
		return fmt.Sprintf("無法獲取日誌: %v", err)
	}
	defer logs.Close()

	buf := make([]byte, 2048)
	n, _ := logs.Read(buf)
	return string(buf[:n])
}

// updateResourceChart 更新資源使用圖表
func (m *K8sManager) updateResourceChart() {
	// 這裡應該使用 metrics API 來獲取實際的資源使用情況
	// 目前使用模擬資料
	m.resourceChart.UpdateData(m.pods)
}

// updatePodResources 更新單個 Pod 的資源資訊
func (m *K8sManager) updatePodResources(pod v1.Pod) {
	// 更新配額顯示
	m.quotaDisplay.UpdatePod(pod)
}

// startMonitoring 開始監控 K8s 資源
func (m *K8sManager) startMonitoring() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		m.refreshPods()
	}
}

// createCard 建立帶標題的卡片容器
func (m *K8sManager) createCard(title string, content fyne.CanvasObject) *fyne.Container {
	titleLabel := widget.NewLabelWithStyle(title, fyne.TextAlignLeading, fyne.TextStyle{Bold: true})
	return container.NewBorder(titleLabel, nil, nil, nil, content)
}

// Content 回傳 UI 內容
func (m *K8sManager) Content() fyne.CanvasObject {
	return m.content
}

// 輔助函數
func int64Ptr(i int64) *int64 {
	return &i
}

// ResourceChart 資源使用圖表元件
type ResourceChart struct {
	widget.BaseWidget
	container *fyne.Container
}

func NewResourceChart() *ResourceChart {
	r := &ResourceChart{}
	r.ExtendBaseWidget(r)

	// 建立圖表 UI
	r.container = container.NewVBox(
		widget.NewLabel("CPU 使用率"),
		widget.NewProgressBar(),
		widget.NewLabel("記憶體使用率"),
		widget.NewProgressBar(),
	)

	return r
}

func (r *ResourceChart) CreateRenderer() fyne.WidgetRenderer {
	return widget.NewSimpleRenderer(r.container)
}

func (r *ResourceChart) UpdateData(pods []v1.Pod) {
	// 更新圖表資料
}

// QuotaDisplay 配額顯示元件
type QuotaDisplay struct {
	widget.BaseWidget
	container *fyne.Container
}

func NewQuotaDisplay() *QuotaDisplay {
	q := &QuotaDisplay{}
	q.ExtendBaseWidget(q)

	// 建立配額顯示 UI
	q.container = container.NewVBox(
		widget.NewLabel("CPU 限制: --"),
		widget.NewLabel("記憶體限制: --"),
		widget.NewLabel("儲存限制: --"),
	)

	return q
}

func (q *QuotaDisplay) CreateRenderer() fyne.WidgetRenderer {
	return widget.NewSimpleRenderer(q.container)
}

func (q *QuotaDisplay) UpdatePod(pod v1.Pod) {
	// 更新配額資訊
}
