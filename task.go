// task_runner.go - 任務執行器模組，整合 Makefile 和 Taskfile
package main

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
	"gopkg.in/yaml.v3"
)

// TaskRunnerConfig 任務執行器配置
type TaskRunnerConfig struct {
	ProjectPaths []string // 專案路徑列表
	DefaultTool  string   // 預設工具: "make" 或 "task"
	Environment  map[string]string
}

// Task 代表一個可執行的任務
type Task struct {
	Name        string
	Description string
	Command     string
	Category    string
	Tool        string // "make" 或 "task"
	Path        string // 專案路徑
	Params      []TaskParam
	DependsOn   []string
}

// TaskParam 任務參數
type TaskParam struct {
	Name         string
	Description  string
	DefaultValue string
	Required     bool
}

// TaskExecution 任務執行記錄
type TaskExecution struct {
	Task      *Task
	StartTime time.Time
	EndTime   time.Time
	Output    string
	Error     error
	Status    string // "running", "success", "failed"
}

// TaskRunner 任務執行器
type TaskRunner struct {
	config      *TaskRunnerConfig
	tasks       []Task
	executions  []TaskExecution
	currentExec *TaskExecution
	execMutex   sync.Mutex

	// UI 元件
	content     fyne.CanvasObject
	taskList    *widget.List
	outputView  *widget.Entry
	paramForm   *fyne.Container
	progressBar *widget.ProgressBar
	statusLabel *widget.Label

	// 資料綁定
	selectedTask int
	searchFilter binding.String
	outputBuffer binding.String

	// 執行控制
	cancelFunc context.CancelFunc
}

// NewTaskRunner 建立新的任務執行器
func NewTaskRunner(config *TaskRunnerConfig) *TaskRunner {
	r := &TaskRunner{
		config:       config,
		tasks:        []Task{},
		executions:   []TaskExecution{},
		selectedTask: -1,
		searchFilter: binding.NewString(),
		outputBuffer: binding.NewString(),
	}

	// 掃描並載入任務
	r.scanTasks()

	// 建立 UI
	r.buildUI()

	return r
}

// scanTasks 掃描 Makefile 和 Taskfile
func (r *TaskRunner) scanTasks() {
	r.tasks = []Task{}

	for _, projectPath := range r.config.ProjectPaths {
		// 掃描 Makefile
		makefilePath := filepath.Join(projectPath, "Makefile")
		if tasks, err := r.parseMakefile(makefilePath); err == nil {
			r.tasks = append(r.tasks, tasks...)
		}

		// 掃描 Taskfile
		taskfilePath := filepath.Join(projectPath, "Taskfile.yml")
		if tasks, err := r.parseTaskfile(taskfilePath); err == nil {
			r.tasks = append(r.tasks, tasks...)
		}
	}

	// 依照類別和名稱排序任務
	r.sortTasks()
}

// parseMakefile 解析 Makefile
func (r *TaskRunner) parseMakefile(path string) ([]Task, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	tasks := []Task{}
	scanner := bufio.NewScanner(file)

	// Makefile 解析的正規表達式
	targetRegex := regexp.MustCompile(`^([a-zA-Z0-9_-]+):`)
	commentRegex := regexp.MustCompile(`^\s*##\s*(.+)`)
	categoryRegex := regexp.MustCompile(`^\s*##\s*@category\s+(.+)`)

	var currentTask *Task
	var currentCategory string

	for scanner.Scan() {
		line := scanner.Text()

		// 檢查是否為類別註解
		if matches := categoryRegex.FindStringSubmatch(line); matches != nil {
			currentCategory = matches[1]
			continue
		}

		// 檢查是否為任務描述
		if matches := commentRegex.FindStringSubmatch(line); matches != nil && currentTask == nil {
			// 下一個目標將使用這個描述
			continue
		}

		// 檢查是否為 Make 目標
		if matches := targetRegex.FindStringSubmatch(line); matches != nil {
			if currentTask != nil {
				tasks = append(tasks, *currentTask)
			}

			taskName := matches[1]
			// 忽略特殊目標
			if taskName == ".PHONY" || strings.HasPrefix(taskName, ".") {
				currentTask = nil
				continue
			}

			currentTask = &Task{
				Name:     taskName,
				Tool:     "make",
				Path:     filepath.Dir(path),
				Category: currentCategory,
				Command:  fmt.Sprintf("make %s", taskName),
			}

			// 檢查前一行是否有描述
			// 這裡簡化處理，實際應該要更複雜的解析
		}
	}

	if currentTask != nil {
		tasks = append(tasks, *currentTask)
	}

	return tasks, nil
}

// parseTaskfile 解析 Taskfile.yml
func (r *TaskRunner) parseTaskfile(path string) ([]Task, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}

	// 解析 YAML
	var taskfile struct {
		Version string `yaml:"version"`
		Tasks   map[string]struct {
			Desc          string            `yaml:"desc"`
			Cmds          []string          `yaml:"cmds"`
			Deps          []string          `yaml:"deps"`
			Vars          map[string]string `yaml:"vars"`
			Preconditions []string          `yaml:"preconditions"`
		} `yaml:"tasks"`
	}

	if err := yaml.Unmarshal(data, &taskfile); err != nil {
		return nil, err
	}

	tasks := []Task{}
	for name, taskDef := range taskfile.Tasks {
		task := Task{
			Name:        name,
			Description: taskDef.Desc,
			Tool:        "task",
			Path:        filepath.Dir(path),
			Command:     fmt.Sprintf("task %s", name),
			DependsOn:   taskDef.Deps,
		}

		// 解析參數（從變數中提取）
		for varName, varDefault := range taskDef.Vars {
			task.Params = append(task.Params, TaskParam{
				Name:         varName,
				DefaultValue: varDefault,
				Required:     false,
			})
		}

		tasks = append(tasks, task)
	}

	return tasks, nil
}

// sortTasks 排序任務
func (r *TaskRunner) sortTasks() {
	// 依照類別和名稱排序
	// 實作排序邏輯
}

// buildUI 建立使用者介面
func (r *TaskRunner) buildUI() {
	// 先建立任務列表
	r.createTaskList()

	// 建立搜尋欄（現在 taskList 已經存在）
	searchBar := r.createSearchBar()

	// 建立執行面板
	execPanel := r.createExecutionPanel()

	// 建立輸出視圖
	r.createOutputView()

	// 建立歷史記錄面板
	historyPanel := r.createHistoryPanel()

	// 組合佈局
	leftPanel := container.NewBorder(
		searchBar,
		nil,
		nil,
		nil,
		r.taskList,
	)

	rightPanel := container.NewVSplit(
		execPanel,
		container.NewAppTabs(
			container.NewTabItem("輸出", r.outputView),
			container.NewTabItem("歷史", historyPanel),
		),
	)
	rightPanel.SetOffset(0.3)

	r.content = container.NewHSplit(leftPanel, rightPanel)
}

// createSearchBar 建立搜尋欄
func (r *TaskRunner) createSearchBar() *fyne.Container {
	searchEntry := widget.NewEntryWithData(r.searchFilter)
	searchEntry.SetPlaceHolder("搜尋任務...")

	// 即時過濾
	r.searchFilter.AddListener(binding.NewDataListener(func() {
		r.taskList.Refresh()
	}))

	refreshBtn := widget.NewButtonWithIcon("", theme.ViewRefreshIcon(), func() {
		r.scanTasks()
		r.taskList.Refresh()
	})

	return container.NewBorder(nil, nil, nil, refreshBtn, searchEntry)
}

// createTaskList 建立任務列表
func (r *TaskRunner) createTaskList() {
	r.taskList = widget.NewList(
		func() int {
			filter, _ := r.searchFilter.Get()
			if filter == "" {
				return len(r.tasks)
			}

			count := 0
			for _, task := range r.tasks {
				if r.matchFilter(task, filter) {
					count++
				}
			}
			return count
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				container.NewHBox(
					widget.NewIcon(theme.MediaPlayIcon()),
					widget.NewLabelWithStyle("任務名稱", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
				),
				widget.NewLabel("任務描述"),
				container.NewHBox(
					widget.NewLabel("工具: "),
					widget.NewLabel("路徑: "),
				),
			)
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			container := item.(*fyne.Container)
			task := r.getFilteredTask(id)

			// 更新任務資訊
			header := container.Objects[0].(*fyne.Container)
			icon := header.Objects[0].(*widget.Icon)
			nameLabel := header.Objects[1].(*widget.Label)

			// 根據工具類型設定圖示
			if task.Tool == "make" {
				icon.SetResource(theme.DocumentIcon())
			} else {
				icon.SetResource(theme.ComputerIcon())
			}

			nameLabel.SetText(task.Name)

			descLabel := container.Objects[1].(*widget.Label)
			descLabel.SetText(task.Description)

			infoContainer := container.Objects[2].(*fyne.Container)
			infoContainer.Objects[0].(*widget.Label).SetText(fmt.Sprintf("工具: %s", task.Tool))
			infoContainer.Objects[1].(*widget.Label).SetText(fmt.Sprintf("路徑: %s", filepath.Base(task.Path)))
		},
	)

	// 選擇任務時更新執行面板
	r.taskList.OnSelected = func(id widget.ListItemID) {
		r.selectedTask = id
		r.updateExecutionPanel()
	}
}

// createExecutionPanel 建立執行面板
func (r *TaskRunner) createExecutionPanel() fyne.CanvasObject {
	// 任務資訊
	taskInfoLabel := widget.NewLabel("選擇一個任務開始執行")

	// 參數表單容器
	r.paramForm = container.NewVBox()

	// 執行按鈕
	runBtn := widget.NewButtonWithIcon("執行", theme.MediaPlayIcon(), r.executeTask)
	stopBtn := widget.NewButtonWithIcon("停止", theme.MediaStopIcon(), r.stopTask)
	stopBtn.Disable()

	// 進度條
	r.progressBar = widget.NewProgressBar()
	r.progressBar.Hide()

	// 狀態標籤
	r.statusLabel = widget.NewLabel("")

	// 組合面板
	return r.createCard("任務執行",
		container.NewVBox(
			taskInfoLabel,
			widget.NewSeparator(),
			r.paramForm,
			container.NewHBox(runBtn, stopBtn),
			r.progressBar,
			r.statusLabel,
		),
	)
}

// createOutputView 建立輸出視圖
func (r *TaskRunner) createOutputView() {
	r.outputView = widget.NewMultiLineEntry()
	r.outputView.Bind(r.outputBuffer)
	r.outputView.Disable() // 只讀

	// 設定等寬字型樣式
	r.outputView.TextStyle = fyne.TextStyle{Monospace: true}
}

// createHistoryPanel 建立歷史記錄面板
func (r *TaskRunner) createHistoryPanel() *fyne.Container {
	// 歷史記錄表格
	historyTable := widget.NewTable(
		func() (int, int) {
			return len(r.executions), 4 // 任務名稱、開始時間、耗時、狀態
		},
		func() fyne.CanvasObject {
			return widget.NewLabel("載入中...")
		},
		func(id widget.TableCellID, cell fyne.CanvasObject) {
			label := cell.(*widget.Label)
			if id.Row >= len(r.executions) {
				return
			}

			exec := r.executions[len(r.executions)-1-id.Row] // 最新的在上面

			switch id.Col {
			case 0:
				label.SetText(exec.Task.Name)
			case 1:
				label.SetText(exec.StartTime.Format("15:04:05"))
			case 2:
				if !exec.EndTime.IsZero() {
					duration := exec.EndTime.Sub(exec.StartTime)
					label.SetText(duration.Round(time.Millisecond).String())
				} else {
					label.SetText("執行中...")
				}
			case 3:
				label.SetText(exec.Status)
				// 根據狀態設定顏色
				switch exec.Status {
				case "success":
					label.TextStyle = fyne.TextStyle{Bold: true}
				case "failed":
					label.TextStyle = fyne.TextStyle{Bold: true}
				}
			}
		},
	)

	// 清空歷史按鈕
	clearBtn := widget.NewButton("清空歷史", func() {
		r.executions = []TaskExecution{}
		historyTable.Refresh()
	})

	return container.NewBorder(
		nil,
		clearBtn,
		nil,
		nil,
		historyTable,
	)
}

// matchFilter 檢查任務是否符合過濾條件
func (r *TaskRunner) matchFilter(task Task, filter string) bool {
	filter = strings.ToLower(filter)
	return strings.Contains(strings.ToLower(task.Name), filter) ||
		strings.Contains(strings.ToLower(task.Description), filter) ||
		strings.Contains(strings.ToLower(task.Category), filter)
}

// getFilteredTask 獲取過濾後的任務
func (r *TaskRunner) getFilteredTask(index int) Task {
	filter, _ := r.searchFilter.Get()
	if filter == "" {
		return r.tasks[index]
	}

	filtered := []Task{}
	for _, task := range r.tasks {
		if r.matchFilter(task, filter) {
			filtered = append(filtered, task)
		}
	}

	if index < len(filtered) {
		return filtered[index]
	}
	return Task{}
}

// updateExecutionPanel 更新執行面板
func (r *TaskRunner) updateExecutionPanel() {
	if r.selectedTask < 0 {
		return
	}

	task := r.getFilteredTask(r.selectedTask)

	// 清空參數表單
	r.paramForm.Objects = []fyne.CanvasObject{}

	// 添加任務資訊
	r.paramForm.Add(widget.NewLabelWithStyle(task.Name, fyne.TextAlignLeading, fyne.TextStyle{Bold: true}))
	r.paramForm.Add(widget.NewLabel(task.Description))
	r.paramForm.Add(widget.NewSeparator())

	// 如果有參數，建立輸入表單
	if len(task.Params) > 0 {
		r.paramForm.Add(widget.NewLabel("參數："))
		for _, param := range task.Params {
			entry := widget.NewEntry()
			entry.SetPlaceHolder(param.Description)
			entry.SetText(param.DefaultValue)

			r.paramForm.Add(container.NewBorder(
				nil, nil,
				widget.NewLabel(param.Name+": "),
				nil,
				entry,
			))
		}
	}

	r.paramForm.Refresh()
}

// executeTask 執行任務
func (r *TaskRunner) executeTask() {
	if r.selectedTask < 0 {
		return
	}

	task := r.getFilteredTask(r.selectedTask)

	// 建立執行記錄
	exec := &TaskExecution{
		Task:      &task,
		StartTime: time.Now(),
		Status:    "running",
	}

	r.execMutex.Lock()
	r.currentExec = exec
	r.executions = append(r.executions, *exec)
	r.execMutex.Unlock()

	// 清空輸出
	r.outputBuffer.Set("")

	// 顯示進度條
	r.progressBar.Show()
	r.progressBar.SetValue(0.5) // 不確定進度
	r.statusLabel.SetText("執行中: " + task.Name)

	// 在背景執行任務
	go r.runTask(exec)
}

// runTask 實際執行任務
func (r *TaskRunner) runTask(execution *TaskExecution) {
	// 建立上下文用於取消
	ctx, cancel := context.WithCancel(context.Background())
	r.cancelFunc = cancel
	defer cancel()

	// 切換到任務目錄
	originalDir, _ := os.Getwd()
	os.Chdir(execution.Task.Path)
	defer os.Chdir(originalDir)

	// 準備命令
	var cmd *exec.Cmd
	if execution.Task.Tool == "make" {
		cmd = exec.CommandContext(ctx, "make", execution.Task.Name)
	} else {
		cmd = exec.CommandContext(ctx, "task", execution.Task.Name)
	}

	// 設定環境變數
	cmd.Env = os.Environ()
	for k, v := range r.config.Environment {
		cmd.Env = append(cmd.Env, fmt.Sprintf("%s=%s", k, v))
	}

	// 捕獲輸出
	stdout, _ := cmd.StdoutPipe()
	stderr, _ := cmd.StderrPipe()

	// 開始執行
	if err := cmd.Start(); err != nil {
		r.finishExecution(execution, err)
		return
	}

	// 讀取輸出
	var wg sync.WaitGroup
	wg.Add(2)

	go r.readOutput(stdout, &wg)
	go r.readOutput(stderr, &wg)

	// 等待完成
	wg.Wait()
	err := cmd.Wait()

	r.finishExecution(execution, err)
}

// readOutput 讀取並顯示輸出
func (r *TaskRunner) readOutput(reader io.Reader, wg *sync.WaitGroup) {
	defer wg.Done()

	scanner := bufio.NewScanner(reader)
	for scanner.Scan() {
		line := scanner.Text()
		current, _ := r.outputBuffer.Get()
		r.outputBuffer.Set(current + line + "\n")
	}
}

// finishExecution 完成執行
func (r *TaskRunner) finishExecution(exec *TaskExecution, err error) {
	r.execMutex.Lock()
	defer r.execMutex.Unlock()

	exec.EndTime = time.Now()
	exec.Error = err

	if err != nil {
		exec.Status = "failed"
		r.statusLabel.SetText("執行失敗: " + err.Error())
	} else {
		exec.Status = "success"
		r.statusLabel.SetText("執行成功")
	}

	// 更新執行記錄
	for i := range r.executions {
		if r.executions[i].StartTime == exec.StartTime {
			r.executions[i] = *exec
			break
		}
	}

	r.progressBar.Hide()
	r.currentExec = nil
	r.cancelFunc = nil
}

// stopTask 停止任務
func (r *TaskRunner) stopTask() {
	if r.cancelFunc != nil {
		r.cancelFunc()
		r.statusLabel.SetText("任務已取消")
	}
}

// createCard 建立帶標題的卡片容器
func (r *TaskRunner) createCard(title string, content fyne.CanvasObject) *fyne.Container {
	titleLabel := widget.NewLabelWithStyle(title, fyne.TextAlignLeading, fyne.TextStyle{Bold: true})
	return container.NewBorder(titleLabel, nil, nil, nil, content)
}

// Content 回傳 UI 內容
func (r *TaskRunner) Content() fyne.CanvasObject {
	return r.content
}
