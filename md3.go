// theme.go - Material Design 3 inspired theme for Koopa's Dev Assistant
package main

import (
	"image/color"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

// KoopaMD3Theme 實現 Material Design 3 風格的主題
type KoopaMD3Theme struct{}

// Color 回傳主題顏色
func (m *KoopaMD3Theme) Color(name fyne.ThemeColorName, variant fyne.ThemeVariant) color.Color {
	// 駭客風格深色主題 - 使用綠色和藍色作為主色調
	// 強制使用深色模式以獲得駭客感
	switch name {
	case theme.ColorNameBackground:
		return color.NRGBA{R: 10, G: 15, B: 20, A: 255} // 深藍黑色背景
	case theme.ColorNameForeground:
		return color.NRGBA{R: 0, G: 255, B: 150, A: 255} // 駭客綠色文字
	case theme.ColorNameButton:
		return color.NRGBA{R: 0, G: 120, B: 180, A: 255} // 科技藍色按鈕
	case theme.ColorNamePrimary:
		return color.NRGBA{R: 0, G: 255, B: 150, A: 255} // 主要綠色
	case theme.ColorNameHover:
		return color.NRGBA{R: 20, G: 30, B: 40, A: 255} // 懸停深色
	case theme.ColorNameFocus:
		return color.NRGBA{R: 0, G: 200, B: 255, A: 100} // 聚焦藍色
	case theme.ColorNameSelection:
		return color.NRGBA{R: 0, G: 150, B: 255, A: 80} // 選擇藍色
	case theme.ColorNameScrollBar:
		return color.NRGBA{R: 0, G: 255, B: 150, A: 150} // 滾動條綠色
	case theme.ColorNameShadow:
		return color.NRGBA{R: 0, G: 255, B: 150, A: 30} // 綠色陰影
	case theme.ColorNameSuccess:
		return color.NRGBA{R: 0, G: 255, B: 100, A: 255} // 成功亮綠色
	case theme.ColorNameWarning:
		return color.NRGBA{R: 255, G: 200, B: 0, A: 255} // 警告黃色
	case theme.ColorNameError:
		return color.NRGBA{R: 255, G: 50, B: 50, A: 255} // 錯誤紅色
	case theme.ColorNameInputBackground:
		return color.NRGBA{R: 15, G: 25, B: 35, A: 255} // 輸入框深色背景
	case theme.ColorNamePlaceHolder:
		return color.NRGBA{R: 100, G: 150, B: 100, A: 255} // 佔位符暗綠色
	case theme.ColorNamePressed:
		return color.NRGBA{R: 0, G: 180, B: 255, A: 255} // 按下時亮藍色
	case theme.ColorNameDisabled:
		return color.NRGBA{R: 50, G: 70, B: 50, A: 255} // 禁用暗色
	case theme.ColorNameDisabledButton:
		return color.NRGBA{R: 30, G: 40, B: 50, A: 255} // 禁用按鈕
	case theme.ColorNameHeaderBackground:
		return color.NRGBA{R: 5, G: 10, B: 15, A: 255} // 標題背景更深
	case theme.ColorNameMenuBackground:
		return color.NRGBA{R: 15, G: 20, B: 25, A: 255} // 選單背景
	case theme.ColorNameOverlayBackground:
		return color.NRGBA{R: 0, G: 0, B: 0, A: 180} // 覆蓋層背景
	case theme.ColorNameSeparator:
		return color.NRGBA{R: 0, G: 100, B: 150, A: 255} // 分隔線藍綠色
	}

	return theme.DefaultTheme().Color(name, variant)
}

// Font 回傳字型資源
func (m *KoopaMD3Theme) Font(style fyne.TextStyle) fyne.Resource {
	// 可以在這裡加入自定義字型
	// 建議使用 Roboto 或 Inter 字型以符合 Material Design
	return theme.DefaultTheme().Font(style)
}

// Icon 回傳圖示資源
func (m *KoopaMD3Theme) Icon(name fyne.ThemeIconName) fyne.Resource {
	// 可以在這裡加入自定義圖示
	return theme.DefaultTheme().Icon(name)
}

// Size 回傳主題尺寸
func (m *KoopaMD3Theme) Size(name fyne.ThemeSizeName) float32 {
	// Material Design 3 使用更大的觸控目標和更寬鬆的間距
	switch name {
	case theme.SizeNameCaptionText:
		return 11 // 標題文字
	case theme.SizeNameInlineIcon:
		return 20 // 行內圖示
	case theme.SizeNameInnerPadding:
		return 8 // 內部間距
	case theme.SizeNameLineSpacing:
		return 4 // 行距
	case theme.SizeNamePadding:
		return 12 // 一般間距
	case theme.SizeNameScrollBar:
		return 16 // 滾動條寬度
	case theme.SizeNameScrollBarSmall:
		return 8 // 小型滾動條
	case theme.SizeNameSeparatorThickness:
		return 1 // 分隔線厚度
	case theme.SizeNameText:
		return 14 // 一般文字大小
	case theme.SizeNameHeadingText:
		return 24 // 標題文字
	case theme.SizeNameSubHeadingText:
		return 18 // 副標題文字
	case theme.SizeNameInputBorder:
		return 2 // 輸入框邊框
	case theme.SizeNameInputRadius:
		return 12 // 圓角半徑 (MD3 特色)
	}

	return theme.DefaultTheme().Size(name)
}

// StyledButton 自定義元件樣式
type StyledButton struct {
	widget.Button
	importance string // "primary", "secondary", "tertiary"
}

func NewStyledButton(label string, importance string, tapped func()) *StyledButton {
	btn := &StyledButton{
		importance: importance,
	}
	btn.Text = label
	btn.OnTapped = tapped
	btn.ExtendBaseWidget(btn)
	return btn
}

// StatusBar 狀態列元件
type StatusBar struct {
	widget.BaseWidget
	statusLabel *widget.Label
	content     *fyne.Container
}

func NewStatusBar() *StatusBar {
	s := &StatusBar{
		statusLabel: widget.NewLabel("就緒"),
	}

	// 建立狀態列佈局
	s.content = container.NewBorder(
		nil, nil,
		container.NewHBox(
			widget.NewIcon(theme.InfoIcon()),
			s.statusLabel,
		),
		container.NewHBox(
			widget.NewLabel("Koopa's Assistant v1.0"),
		),
		nil,
	)

	s.ExtendBaseWidget(s)
	return s
}

func (s *StatusBar) SetText(text string) {
	s.statusLabel.SetText(text)
}

func (s *StatusBar) CreateRenderer() fyne.WidgetRenderer {
	return widget.NewSimpleRenderer(s.content)
}

// Card MD3 風格的卡片元件
type Card struct {
	widget.BaseWidget
	Title   string
	Content fyne.CanvasObject
	Actions []fyne.CanvasObject
}

func NewCard(title string, content fyne.CanvasObject, actions ...fyne.CanvasObject) *Card {
	c := &Card{
		Title:   title,
		Content: content,
		Actions: actions,
	}
	c.ExtendBaseWidget(c)
	return c
}

func (c *Card) CreateRenderer() fyne.WidgetRenderer {
	// 建立標題
	titleLabel := widget.NewLabelWithStyle(c.Title, fyne.TextAlignLeading, fyne.TextStyle{Bold: true})

	// 建立動作列
	var actionContainer *fyne.Container
	if len(c.Actions) > 0 {
		actionContainer = container.NewHBox(c.Actions...)
	}

	// 組合卡片內容
	var cardContent *fyne.Container
	if actionContainer != nil {
		cardContent = container.NewVBox(
			titleLabel,
			widget.NewSeparator(),
			c.Content,
			widget.NewSeparator(),
			actionContainer,
		)
	} else {
		cardContent = container.NewVBox(
			titleLabel,
			widget.NewSeparator(),
			c.Content,
		)
	}

	// 加入內邊距
	paddedContent := container.NewPadded(cardContent)

	// 建立帶陰影效果的背景 - 駭客風格
	background := canvas.NewRectangle(color.NRGBA{R: 15, G: 25, B: 35, A: 255}) // 深色背景
	background.StrokeColor = color.NRGBA{R: 0, G: 255, B: 150, A: 100} // 綠色邊框
	background.StrokeWidth = 2
	background.CornerRadius = 8 // 較小的圓角更有科技感

	return &cardRenderer{
		card:       c,
		background: background,
		content:    paddedContent,
	}
}

type cardRenderer struct {
	card       *Card
	background *canvas.Rectangle
	content    *fyne.Container
}

func (r *cardRenderer) Layout(size fyne.Size) {
	r.background.Resize(size)
	r.content.Resize(size)
}

func (r *cardRenderer) MinSize() fyne.Size {
	contentSize := r.content.MinSize()
	// 確保卡片有最小尺寸，讓佈局更美觀
	minWidth := float32(280)
	minHeight := float32(180)

	if contentSize.Width > minWidth {
		minWidth = contentSize.Width
	}
	if contentSize.Height > minHeight {
		minHeight = contentSize.Height
	}

	return fyne.NewSize(minWidth, minHeight)
}

func (r *cardRenderer) Refresh() {
	r.background.Refresh()
	r.content.Refresh()
}

func (r *cardRenderer) Objects() []fyne.CanvasObject {
	return []fyne.CanvasObject{r.background, r.content}
}

func (r *cardRenderer) Destroy() {}
