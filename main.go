// main.go - Koopa's Personal Development Assistant
package main

import (
	"log"
	"os"
	"path/filepath"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

// AppConfig 儲存應用程式的全域配置
type AppConfig struct {
	K8sConfig      *K8sConfig
	DBConfig       *DatabaseConfig
	AIConfig       *AIConfig
	TaskRunnerConf *TaskRunnerConfig
}

// Application 是主要的應用程式結構
type Application struct {
	app    fyne.App
	window fyne.Window
	config *AppConfig

	// 核心模組
	k8sManager    *K8sManager
	dbManager     *DatabaseManager
	mcpVisualizer *MCPVisualizer
	taskRunner    *TaskRunner
	aiAssistant   *AIAssistant

	// UI 元件
	mainContent *fyne.Container
	sidebar     *widget.List
	statusBar   *StatusBar
}

// NewApplication 建立新的應用程式實例
func NewApplication() *Application {
	// 建立應用程式並套用自定義主題
	myApp := app.NewWithID("io.koopa.devassistant")
	myApp.Settings().SetTheme(&KoopaMD3Theme{})

	// 建立主視窗
	window := myApp.NewWindow("🚀 Koopa's Cyber Development Assistant 🚀")
	window.Resize(fyne.NewSize(1600, 1000))
	window.SetIcon(theme.ComputerIcon())

	app := &Application{
		app:    myApp,
		window: window,
		config: loadConfig(),
	}

	// 初始化各個模組
	app.initializeModules()

	// 建立UI
	app.buildUI()

	return app
}

// initializeModules 初始化所有功能模組
func (a *Application) initializeModules() {
	// K8s 管理器
	a.k8sManager = NewK8sManager(a.config.K8sConfig)

	// 資料庫管理器
	a.dbManager = NewDatabaseManager(a.config.DBConfig)

	// MCP 視覺化工具
	a.mcpVisualizer = NewMCPVisualizer()

	// 任務執行器（整合 Makefile/Taskfile）
	a.taskRunner = NewTaskRunner(a.config.TaskRunnerConf)

	// AI 助手（預留介面，後續整合）
	a.aiAssistant = NewAIAssistant(a.config.AIConfig)
}

// buildUI 建立使用者介面
func (a *Application) buildUI() {
	// 建立側邊欄
	a.sidebar = a.createSidebar()

	// 建立主要內容區域
	a.mainContent = container.NewStack()

	// 建立狀態列
	a.statusBar = NewStatusBar()

	// 建立頂部工具列
	toolbar := a.createToolbar()

	// 組合主要佈局
	content := container.NewBorder(
		toolbar,       // 頂部
		a.statusBar,   // 底部
		a.sidebar,     // 左側
		nil,           // 右側
		a.mainContent, // 中央
	)

	a.window.SetContent(content)

	// 設定初始視圖
	a.showDashboard()
}

// createSidebar 建立側邊導航欄
func (a *Application) createSidebar() *fyne.Container {
	modules := []struct {
		Name   string
		Icon   fyne.Resource
		Action func()
	}{
		{"🏠 Control Center", theme.HomeIcon(), a.showDashboard},
		{"☸️ K8s Cluster", theme.ComputerIcon(), a.showK8sManager},
		{"🗄️ Database", theme.StorageIcon(), a.showDBManager},
		{"⚙️ Task Engine", theme.SettingsIcon(), a.showTaskRunner},
		{"🔗 MCP Monitor", theme.InfoIcon(), a.showMCPVisualizer},
		{"🤖 AI Brain", theme.HelpIcon(), a.showAIAssistant},
	}

	// 創建側邊欄標題
	title := widget.NewLabelWithStyle(
		">>> MODULES <<<",
		fyne.TextAlignCenter,
		fyne.TextStyle{Bold: true, Monospace: true},
	)

	// 創建模組按鈕
	var buttons []fyne.CanvasObject
	buttons = append(buttons, title)
	buttons = append(buttons, widget.NewSeparator())

	for i, module := range modules {
		moduleIndex := i // 捕獲循環變數

		// 創建自定義按鈕
		btn := widget.NewButtonWithIcon(module.Name, module.Icon, func() {
			modules[moduleIndex].Action()
			a.statusBar.SetText(">>> " + modules[moduleIndex].Name + " ACTIVATED <<<")
		})

		// 設定按鈕樣式
		btn.Importance = widget.MediumImportance

		buttons = append(buttons, btn)

		// 在每個按鈕之間添加小間距
		if i < len(modules)-1 {
			spacer := widget.NewLabel("")
			spacer.Resize(fyne.NewSize(0, 5))
			buttons = append(buttons, spacer)
		}
	}

	// 添加底部狀態
	buttons = append(buttons, widget.NewSeparator())
	statusLabel := widget.NewLabelWithStyle(
		"SYSTEM READY",
		fyne.TextAlignCenter,
		fyne.TextStyle{Monospace: true},
	)
	buttons = append(buttons, statusLabel)

	// 創建側邊欄容器
	sidebar := container.NewVBox(buttons...)

	// 設定最小寬度
	sidebar.Resize(fyne.NewSize(280, 0))

	return sidebar
}

// createToolbar 建立頂部工具列
func (a *Application) createToolbar() *widget.Toolbar {
	return widget.NewToolbar(
		widget.NewToolbarAction(theme.DocumentCreateIcon(), func() {
			log.Println("新建項目")
		}),
		widget.NewToolbarSeparator(),
		widget.NewToolbarAction(theme.ViewRefreshIcon(), func() {
			a.refreshCurrentView()
		}),
		widget.NewToolbarAction(theme.SettingsIcon(), func() {
			a.showSettings()
		}),
		widget.NewToolbarSpacer(),
		widget.NewToolbarAction(theme.HelpIcon(), func() {
			a.showHelp()
		}),
	)
}

// View switching methods
func (a *Application) showDashboard() {
	dashboard := NewDashboard(a)
	a.mainContent.Objects = []fyne.CanvasObject{dashboard.Content()}
	a.mainContent.Refresh()
}

func (a *Application) showK8sManager() {
	a.mainContent.Objects = []fyne.CanvasObject{a.k8sManager.Content()}
	a.mainContent.Refresh()
}

func (a *Application) showDBManager() {
	a.mainContent.Objects = []fyne.CanvasObject{a.dbManager.Content()}
	a.mainContent.Refresh()
}

func (a *Application) showTaskRunner() {
	a.mainContent.Objects = []fyne.CanvasObject{a.taskRunner.Content()}
	a.mainContent.Refresh()
}

func (a *Application) showMCPVisualizer() {
	a.mainContent.Objects = []fyne.CanvasObject{a.mcpVisualizer.Content()}
	a.mainContent.Refresh()
}

func (a *Application) showAIAssistant() {
	a.mainContent.Objects = []fyne.CanvasObject{a.aiAssistant.Content()}
	a.mainContent.Refresh()
}

func (a *Application) refreshCurrentView() {
	a.statusBar.SetText("重新整理中...")
	// TODO: 實作重新整理邏輯
}

func (a *Application) showSettings() {
	// TODO: 實作設定視窗
}

func (a *Application) showHelp() {
	// TODO: 實作說明視窗
}

// Run 執行應用程式
func (a *Application) Run() {
	a.window.ShowAndRun()
}

// loadConfig 載入應用程式配置
func loadConfig() *AppConfig {
	return &AppConfig{
		K8sConfig: &K8sConfig{
			KubeConfigPath: filepath.Join(os.Getenv("HOME"), ".kube", "config"),
			CurrentContext: "default",
			Namespace:      "default",
		},
		DBConfig: &DatabaseConfig{
			Host:     "localhost",
			Port:     5432,
			User:     "postgres",
			Password: "",
			Database: "postgres",
			SSLMode:  "disable",
		},
		AIConfig: &AIConfig{
			DefaultProvider: "claude",
			MaxTokens:       4000,
			Temperature:     0.7,
			SystemPrompt:    "你是 Koopa 的個人開發助手，專門協助軟體開發工作。",
		},
		TaskRunnerConf: &TaskRunnerConfig{
			ProjectPaths: []string{".", filepath.Join(os.Getenv("HOME"), "projects")},
			DefaultTool:  "make",
			Environment:  make(map[string]string),
		},
	}
}

func main() {
	app := NewApplication()
	app.Run()
}
