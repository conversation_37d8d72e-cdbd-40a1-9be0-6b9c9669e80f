// mcp.go - Model Context Protocol (MCP) 視覺化工具
package main

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

// MCPServer MCP 伺服器資訊
type MCPServer struct {
	Name        string            `json:"name"`
	Version     string            `json:"version"`
	Status      string            `json:"status"` // "connected", "disconnected", "error"
	Endpoint    string            `json:"endpoint"`
	Capabilities []string          `json:"capabilities"`
	Tools       []MCPTool         `json:"tools"`
	Resources   []MCPResource     `json:"resources"`
	Metadata    map[string]string `json:"metadata"`
	LastSeen    time.Time         `json:"last_seen"`
}

// MCPTool MCP 工具
type MCPTool struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
	Category    string                 `json:"category"`
}

// MCPResource MCP 資源
type MCPResource struct {
	URI         string            `json:"uri"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	MimeType    string            `json:"mime_type"`
	Metadata    map[string]string `json:"metadata"`
}

// MCPMessage MCP 訊息
type MCPMessage struct {
	ID        string                 `json:"id"`
	Method    string                 `json:"method"`
	Params    map[string]interface{} `json:"params"`
	Result    interface{}            `json:"result,omitempty"`
	Error     *MCPError              `json:"error,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Direction string                 `json:"direction"` // "request", "response"
}

// MCPError MCP 錯誤
type MCPError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// MCPVisualizer MCP 視覺化工具
type MCPVisualizer struct {
	servers  []MCPServer
	messages []MCPMessage

	// UI 元件
	content       fyne.CanvasObject
	serverList    *widget.List
	toolList      *widget.List
	resourceList  *widget.List
	messageLog    *widget.List
	serverDetails *fyne.Container

	// 資料綁定
	selectedServer int
	filterText     binding.String
	autoRefresh    binding.Bool
}

// NewMCPVisualizer 建立新的 MCP 視覺化工具
func NewMCPVisualizer() *MCPVisualizer {
	m := &MCPVisualizer{
		servers:        []MCPServer{},
		messages:       []MCPMessage{},
		selectedServer: -1,
		filterText:     binding.NewString(),
		autoRefresh:    binding.NewBool(),
	}

	// 載入模擬資料
	m.loadMockData()

	// 建立 UI
	m.buildUI()

	// 開始自動重新整理
	go m.startAutoRefresh()

	return m
}

// loadMockData 載入模擬資料
func (m *MCPVisualizer) loadMockData() {
	// 模擬 MCP 伺服器
	m.servers = []MCPServer{
		{
			Name:     "Claude Desktop",
			Version:  "1.0.0",
			Status:   "connected",
			Endpoint: "mcp://claude-desktop",
			Capabilities: []string{"tools", "resources", "prompts"},
			Tools: []MCPTool{
				{
					Name:        "file_reader",
					Description: "讀取檔案內容",
					Category:    "filesystem",
					Parameters: map[string]interface{}{
						"path": map[string]interface{}{
							"type":        "string",
							"description": "檔案路徑",
							"required":    true,
						},
					},
				},
				{
					Name:        "code_analyzer",
					Description: "分析程式碼結構",
					Category:    "development",
					Parameters: map[string]interface{}{
						"language": map[string]interface{}{
							"type":        "string",
							"description": "程式語言",
							"required":    true,
						},
					},
				},
			},
			Resources: []MCPResource{
				{
					URI:         "file:///workspace/main.go",
					Name:        "main.go",
					Description: "主程式檔案",
					MimeType:    "text/x-go",
				},
				{
					URI:         "file:///workspace/README.md",
					Name:        "README.md",
					Description: "專案說明文件",
					MimeType:    "text/markdown",
				},
			},
			Metadata: map[string]string{
				"version": "1.0.0",
				"author":  "Anthropic",
			},
			LastSeen: time.Now(),
		},
		{
			Name:     "VS Code Extension",
			Version:  "0.9.0",
			Status:   "connected",
			Endpoint: "mcp://vscode-extension",
			Capabilities: []string{"tools", "resources"},
			Tools: []MCPTool{
				{
					Name:        "workspace_search",
					Description: "搜尋工作區檔案",
					Category:    "search",
				},
			},
			Resources: []MCPResource{},
			Metadata: map[string]string{
				"version": "0.9.0",
				"author":  "Microsoft",
			},
			LastSeen: time.Now().Add(-5 * time.Minute),
		},
	}

	// 模擬訊息
	m.messages = []MCPMessage{
		{
			ID:        "msg-001",
			Method:    "tools/call",
			Direction: "request",
			Params: map[string]interface{}{
				"name": "file_reader",
				"arguments": map[string]interface{}{
					"path": "/workspace/main.go",
				},
			},
			Timestamp: time.Now().Add(-2 * time.Minute),
		},
		{
			ID:        "msg-002",
			Method:    "tools/call",
			Direction: "response",
			Result: map[string]interface{}{
				"content": "package main\n\nimport (...",
			},
			Timestamp: time.Now().Add(-2 * time.Minute),
		},
	}
}

// buildUI 建立使用者介面
func (m *MCPVisualizer) buildUI() {
	// 建立伺服器列表
	m.createServerList()

	// 建立工具列表
	m.createToolList()

	// 建立資源列表
	m.createResourceList()

	// 建立訊息日誌
	m.createMessageLog()

	// 建立伺服器詳細資訊
	m.createServerDetails()

	// 建立控制面板
	controlPanel := m.createControlPanel()

	// 左側面板 - 伺服器和詳細資訊
	leftPanel := container.NewVSplit(
		NewCard("MCP 伺服器", m.serverList),
		NewCard("伺服器詳細資訊", m.serverDetails),
	)
	leftPanel.SetOffset(0.4)

	// 中間面板 - 工具和資源
	middlePanel := container.NewVSplit(
		NewCard("可用工具", m.toolList),
		NewCard("可用資源", m.resourceList),
	)
	middlePanel.SetOffset(0.5)

	// 右側面板 - 訊息日誌
	rightPanel := NewCard("訊息日誌", m.messageLog)

	// 主要內容區域 - 添加滾動支援
	mainContent := container.NewHSplit(
		container.NewScroll(leftPanel),                                                    // 左側面板添加滾動
		container.NewHSplit(container.NewScroll(middlePanel), container.NewScroll(rightPanel)), // 中間和右側面板添加滾動
	)

	// 組合最終佈局
	m.content = container.NewBorder(
		controlPanel,
		nil,
		nil,
		nil,
		container.NewScroll(mainContent), // 主內容添加滾動
	)
}

// createServerList 建立伺服器列表
func (m *MCPVisualizer) createServerList() {
	m.serverList = widget.NewList(
		func() int {
			return len(m.servers)
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				container.NewHBox(
					widget.NewIcon(theme.ComputerIcon()),
					widget.NewLabelWithStyle("伺服器名稱", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
				),
				widget.NewLabel("狀態"),
				widget.NewLabel("版本"),
			)
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			if id >= len(m.servers) {
				return
			}

			server := m.servers[id]
			container := item.(*fyne.Container)

			// 更新標題
			header := container.Objects[0].(*fyne.Container)
			icon := header.Objects[0].(*widget.Icon)
			nameLabel := header.Objects[1].(*widget.Label)

			// 根據狀態設定圖示
			switch server.Status {
			case "connected":
				icon.SetResource(theme.ConfirmIcon())
			case "disconnected":
				icon.SetResource(theme.CancelIcon())
			case "error":
				icon.SetResource(theme.ErrorIcon())
			}

			nameLabel.SetText(server.Name)

			// 更新狀態和版本
			container.Objects[1].(*widget.Label).SetText(fmt.Sprintf("狀態: %s", server.Status))
			container.Objects[2].(*widget.Label).SetText(fmt.Sprintf("版本: %s", server.Version))
		},
	)

	// 選擇伺服器時更新詳細資訊
	m.serverList.OnSelected = func(id widget.ListItemID) {
		m.selectedServer = id
		m.updateServerDetails()
		m.updateToolList()
		m.updateResourceList()
	}
}

// createToolList 建立工具列表
func (m *MCPVisualizer) createToolList() {
	m.toolList = widget.NewList(
		func() int {
			if m.selectedServer < 0 || m.selectedServer >= len(m.servers) {
				return 0
			}
			return len(m.servers[m.selectedServer].Tools)
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				widget.NewLabelWithStyle("工具名稱", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
				widget.NewLabel("描述"),
				widget.NewLabel("類別"),
			)
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			if m.selectedServer < 0 || m.selectedServer >= len(m.servers) {
				return
			}

			tools := m.servers[m.selectedServer].Tools
			if id >= len(tools) {
				return
			}

			tool := tools[id]
			container := item.(*fyne.Container)

			container.Objects[0].(*widget.Label).SetText(tool.Name)
			container.Objects[1].(*widget.Label).SetText(tool.Description)
			container.Objects[2].(*widget.Label).SetText(fmt.Sprintf("類別: %s", tool.Category))
		},
	)
}

// createResourceList 建立資源列表
func (m *MCPVisualizer) createResourceList() {
	m.resourceList = widget.NewList(
		func() int {
			if m.selectedServer < 0 || m.selectedServer >= len(m.servers) {
				return 0
			}
			return len(m.servers[m.selectedServer].Resources)
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				widget.NewLabelWithStyle("資源名稱", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
				widget.NewLabel("URI"),
				widget.NewLabel("類型"),
			)
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			if m.selectedServer < 0 || m.selectedServer >= len(m.servers) {
				return
			}

			resources := m.servers[m.selectedServer].Resources
			if id >= len(resources) {
				return
			}

			resource := resources[id]
			container := item.(*fyne.Container)

			container.Objects[0].(*widget.Label).SetText(resource.Name)
			container.Objects[1].(*widget.Label).SetText(resource.URI)
			container.Objects[2].(*widget.Label).SetText(resource.MimeType)
		},
	)
}

// createMessageLog 建立訊息日誌
func (m *MCPVisualizer) createMessageLog() {
	m.messageLog = widget.NewList(
		func() int {
			return len(m.messages)
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				container.NewHBox(
					widget.NewIcon(theme.MailSendIcon()),
					widget.NewLabelWithStyle("方法", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
					widget.NewLabel("時間"),
				),
				widget.NewLabel("詳細資訊"),
			)
		},
		func(id widget.ListItemID, item fyne.CanvasObject) {
			if id >= len(m.messages) {
				return
			}

			msg := m.messages[len(m.messages)-1-id] // 最新的在上面
			container := item.(*fyne.Container)

			// 更新標題
			header := container.Objects[0].(*fyne.Container)
			icon := header.Objects[0].(*widget.Icon)
			methodLabel := header.Objects[1].(*widget.Label)
			timeLabel := header.Objects[2].(*widget.Label)

			// 根據方向設定圖示
			if msg.Direction == "request" {
				icon.SetResource(theme.MailSendIcon())
			} else {
				icon.SetResource(theme.MailReplyIcon())
			}

			methodLabel.SetText(msg.Method)
			timeLabel.SetText(msg.Timestamp.Format("15:04:05"))

			// 更新詳細資訊
			detailLabel := container.Objects[1].(*widget.Label)
			if msg.Error != nil {
				detailLabel.SetText(fmt.Sprintf("錯誤: %s", msg.Error.Message))
			} else {
				detailLabel.SetText(fmt.Sprintf("ID: %s", msg.ID))
			}
		},
	)
}

// createServerDetails 建立伺服器詳細資訊
func (m *MCPVisualizer) createServerDetails() {
	m.serverDetails = container.NewVBox(
		widget.NewLabel("選擇一個伺服器查看詳細資訊"),
	)
}

// createControlPanel 建立控制面板
func (m *MCPVisualizer) createControlPanel() *fyne.Container {
	// 重新整理按鈕
	refreshBtn := widget.NewButtonWithIcon("重新整理", theme.ViewRefreshIcon(), func() {
		m.refreshServers()
	})

	// 自動重新整理開關
	autoRefreshCheck := widget.NewCheckWithData("自動重新整理", m.autoRefresh)

	// 過濾輸入
	filterEntry := widget.NewEntryWithData(m.filterText)
	filterEntry.SetPlaceHolder("過濾伺服器...")

	// 連接新伺服器按鈕
	connectBtn := widget.NewButtonWithIcon("連接伺服器", theme.ContentAddIcon(), func() {
		m.showConnectDialog()
	})

	return container.NewHBox(
		refreshBtn,
		autoRefreshCheck,
		widget.NewSeparator(),
		widget.NewLabel("過濾:"),
		filterEntry,
		widget.NewSeparator(),
		connectBtn,
	)
}

// updateServerDetails 更新伺服器詳細資訊
func (m *MCPVisualizer) updateServerDetails() {
	if m.selectedServer < 0 || m.selectedServer >= len(m.servers) {
		return
	}

	server := m.servers[m.selectedServer]

	// 建立詳細資訊內容
	details := container.NewVBox(
		widget.NewLabelWithStyle(server.Name, fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		widget.NewSeparator(),
		widget.NewLabel(fmt.Sprintf("版本: %s", server.Version)),
		widget.NewLabel(fmt.Sprintf("狀態: %s", server.Status)),
		widget.NewLabel(fmt.Sprintf("端點: %s", server.Endpoint)),
		widget.NewLabel(fmt.Sprintf("最後連線: %s", server.LastSeen.Format("15:04:05"))),
		widget.NewSeparator(),
		widget.NewLabel("功能:"),
	)

	// 添加功能列表
	for _, cap := range server.Capabilities {
		details.Add(widget.NewLabel(fmt.Sprintf("• %s", cap)))
	}

	m.serverDetails.Objects = details.Objects
	m.serverDetails.Refresh()
}

// updateToolList 更新工具列表
func (m *MCPVisualizer) updateToolList() {
	m.toolList.Refresh()
}

// updateResourceList 更新資源列表
func (m *MCPVisualizer) updateResourceList() {
	m.resourceList.Refresh()
}

// refreshServers 重新整理伺服器
func (m *MCPVisualizer) refreshServers() {
	// 實際實現中會重新掃描 MCP 伺服器
	m.serverList.Refresh()
}

// showConnectDialog 顯示連接對話框
func (m *MCPVisualizer) showConnectDialog() {
	// TODO: 實現連接新 MCP 伺服器的對話框
}

// startAutoRefresh 開始自動重新整理
func (m *MCPVisualizer) startAutoRefresh() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		autoRefresh, _ := m.autoRefresh.Get()
		if autoRefresh {
			m.refreshServers()
		}
	}
}

// Content 回傳 MCP 視覺化工具內容
func (m *MCPVisualizer) Content() fyne.CanvasObject {
	return m.content
}
